<?php

declare(strict_types=1);

namespace App\Events\Tenant;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Unified Tenant Staff Added Event
 *
 * Handles staff addition for both businesses and delivery providers.
 * Replaces separate StaffAdded and ProviderStaffAdded events.
 */
class TenantStaffAdded implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly User $staffMember,
        public readonly Business|DeliveryProvider $tenant,
        public readonly User $addedBy,
        public readonly string $role,
        public readonly string $temporaryPassword,
        public readonly array $metadata = []
    ) {}

    /**
     * Get the tenant type (business or provider)
     */
    public function getTenantType(): string
    {
        return $this->tenant instanceof Business ? 'business' : 'provider';
    }

    /**
     * Get the tenant ID
     */
    public function getTenantId(): string
    {
        return $this->tenant->id;
    }

    /**
     * Check if this is a business staff addition
     */
    public function isBusinessStaff(): bool
    {
        return $this->tenant instanceof Business;
    }

    /**
     * Check if this is a provider staff addition
     */
    public function isProviderStaff(): bool
    {
        return $this->tenant instanceof DeliveryProvider;
    }

    /**
     * Get broadcast channels
     */
    public function broadcastOn(): array
    {
        $tenantType = $this->getTenantType();
        $tenantId = $this->getTenantId();

        return [
            new PrivateChannel("user.{$this->staffMember->id}"),
            new PrivateChannel("{$tenantType}.{$tenantId}"),
            new PrivateChannel("admin.tenant.management"),
        ];
    }

    /**
     * Get broadcast data
     */
    public function broadcastWith(): array
    {
        return [
            'staff_member_id' => $this->staffMember->id,
            'staff_member_name' => $this->staffMember->full_name,
            'staff_member_email' => $this->staffMember->email,
            'tenant_type' => $this->getTenantType(),
            'tenant_id' => $this->getTenantId(),
            'tenant_name' => $this->tenant->name ?? $this->tenant->company_name ?? $this->tenant->business_name,
            'role' => $this->role,
            'added_by_id' => $this->addedBy->id,
            'added_by_name' => $this->addedBy->full_name,
            'added_at' => now()->toISOString(),
            'metadata' => $this->metadata,
        ];
    }

    /**
     * Get broadcast event name
     */
    public function broadcastAs(): string
    {
        return 'tenant.staff.added';
    }
}
