<?php

declare(strict_types=1);

namespace App\Events\Tenant;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Unified Tenant Status Changed Event
 *
 * Handles status changes for both businesses and delivery providers.
 * Replaces separate BusinessStatusChanged and DeliveryProviderStatusChanged events.
 */
class TenantStatusChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly Business|DeliveryProvider $tenant,
        public readonly string $oldStatus,
        public readonly string $newStatus,
        public readonly ?string $reason = null,
        public readonly ?User $changedBy = null,
        public readonly array $metadata = []
    ) {}

    /**
     * Get the tenant type (business or provider)
     */
    public function getTenantType(): string
    {
        return $this->tenant instanceof Business ? 'business' : 'provider';
    }

    /**
     * Get the tenant ID
     */
    public function getTenantId(): string
    {
        return $this->tenant->id;
    }

    /**
     * Check if this is a business status change
     */
    public function isBusinessStatusChange(): bool
    {
        return $this->tenant instanceof Business;
    }

    /**
     * Check if this is a provider status change
     */
    public function isProviderStatusChange(): bool
    {
        return $this->tenant instanceof DeliveryProvider;
    }

    /**
     * Get status change title for notifications
     */
    public function getStatusChangeTitle(): string
    {
        $tenantType = $this->getTenantType();
        $tenantLabel = $tenantType === 'business' ? 'Business' : 'Provider';
        
        return match($this->newStatus) {
            'active', 'verified' => "{$tenantLabel} Activated",
            'inactive', 'suspended' => "{$tenantLabel} Suspended",
            'pending' => "{$tenantLabel} Under Review",
            'rejected' => "{$tenantLabel} Application Rejected",
            default => "{$tenantLabel} Status Changed"
        };
    }

    /**
     * Get notification type for styling
     */
    public function getNotificationType(): string
    {
        return match($this->newStatus) {
            'active', 'verified' => 'success',
            'inactive', 'suspended', 'rejected' => 'warning',
            'pending' => 'info',
            default => 'info'
        };
    }

    /**
     * Get tenant owner
     */
    public function getTenantOwner(): ?User
    {
        if ($this->tenant instanceof Business) {
            return $this->tenant->user;
        }
        
        if ($this->tenant instanceof DeliveryProvider) {
            return $this->tenant->user;
        }
        
        return null;
    }

    /**
     * Get tenant name
     */
    public function getTenantName(): string
    {
        return $this->tenant->name ?? $this->tenant->company_name ?? $this->tenant->business_name ?? 'Unknown';
    }

    /**
     * Get broadcast channels
     */
    public function broadcastOn(): array
    {
        $tenantType = $this->getTenantType();
        $tenantId = $this->getTenantId();
        $channels = [
            new PrivateChannel("{$tenantType}.{$tenantId}"),
            new PrivateChannel("admin.tenant.management"),
        ];

        // Add owner channel if exists
        $owner = $this->getTenantOwner();
        if ($owner) {
            $channels[] = new PrivateChannel("user.{$owner->id}");
        }

        return $channels;
    }

    /**
     * Get broadcast data
     */
    public function broadcastWith(): array
    {
        return [
            'tenant_type' => $this->getTenantType(),
            'tenant_id' => $this->getTenantId(),
            'tenant_name' => $this->getTenantName(),
            'old_status' => $this->oldStatus,
            'new_status' => $this->newStatus,
            'status_change_title' => $this->getStatusChangeTitle(),
            'notification_type' => $this->getNotificationType(),
            'reason' => $this->reason,
            'changed_by_id' => $this->changedBy?->id,
            'changed_by_name' => $this->changedBy?->full_name,
            'owner_id' => $this->getTenantOwner()?->id,
            'changed_at' => now()->toISOString(),
            'metadata' => $this->metadata,
        ];
    }

    /**
     * Get broadcast event name
     */
    public function broadcastAs(): string
    {
        return 'tenant.status.changed';
    }
}
