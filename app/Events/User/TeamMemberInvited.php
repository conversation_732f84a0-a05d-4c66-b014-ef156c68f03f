<?php

declare(strict_types=1);

namespace App\Events\User;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\TeamInvitation;
use App\Models\User\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Team Member Invited Event
 *
 * Fired when a team member is invited to join a business or delivery provider
 * Triggers invitation notifications and team management updates
 */
class TeamMemberInvited implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly TeamInvitation $invitation,
        public readonly Business|DeliveryProvider $entity,
        public readonly User $invitedBy,
        public readonly string $entityType // 'business' or 'provider'
    ) {}

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            // Entity channel - for team management updates
            new PrivateChannel("{$this->entityType}.{$this->entity->id}.team"),
            // Inviter channel - for invitation status updates
            new PrivateChannel("user.{$this->invitedBy->id}"),
            // Admin channel - for monitoring team invitations
            new PrivateChannel('admin.team-invitations'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'invitation_id' => $this->invitation->id,
            'email' => $this->invitation->email,
            'role' => $this->invitation->role,
            'entity_type' => $this->entityType,
            'entity_id' => $this->entity->id,
            'entity_name' => $this->entityType === 'business' 
                ? $this->entity->business_name 
                : $this->entity->company_name,
            'invited_by' => [
                'id' => $this->invitedBy->id,
                'name' => $this->invitedBy->full_name,
                'email' => $this->invitedBy->email,
            ],
            'expires_at' => $this->invitation->expires_at->toISOString(),
            'tenant_id' => $this->invitation->tenant_id,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'team.member.invited';
    }

    /**
     * Determine if this event should broadcast.
     */
    public function shouldBroadcast(): bool
    {
        return true; // Always broadcast team invitations
    }
}
