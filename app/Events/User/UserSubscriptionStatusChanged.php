<?php

declare(strict_types=1);

namespace App\Events\User;

use App\Enums\UserSubscriptionStatus;
use App\Models\Financial\UserSubscription;
use App\Models\User\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * User Subscription Status Changed Event
 *
 * Triggered when user subscription status changes
 */
class UserSubscriptionStatusChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly UserSubscription $subscription,
        public readonly UserSubscriptionStatus $oldStatus,
        public readonly UserSubscriptionStatus $newStatus,
        public readonly User $user,
        public readonly string $changeType, // 'created', 'renewed', 'expired', 'cancelled', 'upgraded', 'downgraded', 'status_changed'
        public readonly string $reason = '',
        public readonly ?UserSubscription $previousSubscription = null,
        public readonly ?float $amountPaid = null,
        public readonly ?string $paymentReference = null,
        public readonly ?\Carbon\Carbon $gracePeriodEnds = null,
        public readonly ?array $failedPaymentAttempts = null,
        public readonly array $metadata = []
    ) {}

    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("user.{$this->user->id}"),
            new PrivateChannel('admin.subscriptions'),
        ];
    }

    public function broadcastWith(): array
    {
        return [
            'subscription_id' => $this->subscription->id,
            'user_id' => $this->user->id,
            'old_status' => $this->oldStatus->value,
            'new_status' => $this->newStatus->value,
            'change_type' => $this->changeType,
            'reason' => $this->reason,
            'amount_paid' => $this->amountPaid,
            'payment_reference' => $this->paymentReference,
            'grace_period_ends' => $this->gracePeriodEnds?->toISOString(),
            'has_grace_period' => $this->gracePeriodEnds !== null,
            'failed_payment_attempts' => $this->failedPaymentAttempts,
            'previous_subscription_id' => $this->previousSubscription?->id,
            'is_upgrade' => $this->isUpgrade(),
            'is_downgrade' => $this->isDowngrade(),
            'is_renewal' => $this->isRenewal(),
            'is_expiration' => $this->isExpiration(),
            'is_cancellation' => $this->isCancellation(),
            'changed_at' => now()->toISOString(),
            'metadata' => $this->metadata,
        ];
    }

    /**
     * Check if this is an upgrade
     */
    public function isUpgrade(): bool
    {
        return $this->changeType === 'upgraded';
    }

    /**
     * Check if this is a downgrade
     */
    public function isDowngrade(): bool
    {
        return $this->changeType === 'downgraded';
    }

    /**
     * Check if this is a renewal
     */
    public function isRenewal(): bool
    {
        return $this->changeType === 'renewed';
    }

    /**
     * Check if this is an expiration
     */
    public function isExpiration(): bool
    {
        return $this->changeType === 'expired';
    }

    /**
     * Check if this is a cancellation
     */
    public function isCancellation(): bool
    {
        return $this->changeType === 'cancelled';
    }

    /**
     * Check if this is a new subscription
     */
    public function isNewSubscription(): bool
    {
        return $this->changeType === 'created';
    }

    public function broadcastAs(): string
    {
        return 'user.subscription.status.changed';
    }
}
