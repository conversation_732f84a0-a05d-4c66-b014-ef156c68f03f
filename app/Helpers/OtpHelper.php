<?php

declare(strict_types=1);

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;

/**
 * Unified OTP generation and management helper
 * 
 * Consolidates OTP-related functionality across the application
 * to eliminate duplication and ensure consistency.
 */
class OtpHelper
{
    /**
     * Default OTP configuration
     */
    private const DEFAULT_LENGTH = 6;
    private const DEFAULT_TTL = 900; // 15 minutes
    private const DEFAULT_RATE_LIMIT = 60; // 1 minute between requests

    /**
     * Generate a numeric OTP
     */
    public static function generate(int $length = self::DEFAULT_LENGTH): string
    {
        $min = (int) str_repeat('1', $length);
        $max = (int) str_repeat('9', $length);
        
        return str_pad((string) random_int($min, $max), $length, '0', STR_PAD_LEFT);
    }

    /**
     * Generate an alphanumeric OTP
     */
    public static function generateAlphanumeric(int $length = self::DEFAULT_LENGTH): string
    {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $otp = '';
        
        for ($i = 0; $i < $length; $i++) {
            $otp .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        return $otp;
    }

    /**
     * Store OTP in cache with expiration
     */
    public static function store(string $key, string $otp, int $ttl = self::DEFAULT_TTL): bool
    {
        return Cache::put($key, $otp, $ttl);
    }

    /**
     * Retrieve OTP from cache
     */
    public static function retrieve(string $key): ?string
    {
        return Cache::get($key);
    }

    /**
     * Verify OTP against stored value
     */
    public static function verify(string $key, string $otp): bool
    {
        $storedOtp = self::retrieve($key);
        
        if (!$storedOtp) {
            return false;
        }
        
        $isValid = hash_equals($storedOtp, $otp);
        
        // Remove OTP after verification attempt (one-time use)
        if ($isValid) {
            Cache::forget($key);
        }
        
        return $isValid;
    }

    /**
     * Check if rate limit allows new OTP generation
     */
    public static function checkRateLimit(string $rateLimitKey, int $limitSeconds = self::DEFAULT_RATE_LIMIT): bool
    {
        return !Cache::has($rateLimitKey);
    }

    /**
     * Set rate limit for OTP generation
     */
    public static function setRateLimit(string $rateLimitKey, int $limitSeconds = self::DEFAULT_RATE_LIMIT): void
    {
        Cache::put($rateLimitKey, true, $limitSeconds);
    }

    /**
     * Generate and store OTP with rate limiting
     */
    public static function generateAndStore(
        string $cacheKey,
        string $rateLimitKey,
        int $length = self::DEFAULT_LENGTH,
        int $ttl = self::DEFAULT_TTL,
        int $rateLimitSeconds = self::DEFAULT_RATE_LIMIT,
        bool $alphanumeric = false
    ): array {
        // Check rate limit
        if (!self::checkRateLimit($rateLimitKey, $rateLimitSeconds)) {
            return [
                'success' => false,
                'error' => 'Rate limit exceeded. Please wait before requesting a new OTP.',
                'retry_after' => Cache::get($rateLimitKey) ? $rateLimitSeconds : 0,
            ];
        }

        // Generate OTP
        $otp = $alphanumeric ? self::generateAlphanumeric($length) : self::generate($length);

        // Store OTP
        $stored = self::store($cacheKey, $otp, $ttl);

        if (!$stored) {
            return [
                'success' => false,
                'error' => 'Failed to store OTP. Please try again.',
            ];
        }

        // Set rate limit
        self::setRateLimit($rateLimitKey, $rateLimitSeconds);

        return [
            'success' => true,
            'otp' => $otp,
            'expires_in' => $ttl,
        ];
    }

    /**
     * Generate email verification OTP
     */
    public static function generateEmailVerification(string $email): array
    {
        $cacheKey = "email_otp:{$email}";
        $rateLimitKey = "email_otp_rate:{$email}";

        return self::generateAndStore($cacheKey, $rateLimitKey);
    }

    /**
     * Verify email verification OTP
     */
    public static function verifyEmailVerification(string $email, string $otp): bool
    {
        $cacheKey = "email_otp:{$email}";
        return self::verify($cacheKey, $otp);
    }

    /**
     * Generate phone verification OTP
     */
    public static function generatePhoneVerification(string $phoneNumber): array
    {
        $cacheKey = "phone_otp:{$phoneNumber}";
        $rateLimitKey = "phone_otp_rate:{$phoneNumber}";

        return self::generateAndStore($cacheKey, $rateLimitKey);
    }

    /**
     * Verify phone verification OTP
     */
    public static function verifyPhoneVerification(string $phoneNumber, string $otp): bool
    {
        $cacheKey = "phone_otp:{$phoneNumber}";
        return self::verify($cacheKey, $otp);
    }

    /**
     * Generate password reset OTP
     */
    public static function generatePasswordReset(string $email): array
    {
        $cacheKey = "reset_otp:{$email}";
        $rateLimitKey = "reset_otp_rate:{$email}";

        return self::generateAndStore(
            $cacheKey,
            $rateLimitKey,
            ttl: 1800, // 30 minutes for password reset
            rateLimitSeconds: 300 // 5 minutes rate limit
        );
    }

    /**
     * Verify password reset OTP
     */
    public static function verifyPasswordReset(string $email, string $otp): bool
    {
        $cacheKey = "reset_otp:{$email}";
        return self::verify($cacheKey, $otp);
    }

    /**
     * Generate 2FA OTP
     */
    public static function generate2FA(string $userId): array
    {
        $cacheKey = "2fa_otp:{$userId}";
        $rateLimitKey = "2fa_otp_rate:{$userId}";

        return self::generateAndStore(
            $cacheKey,
            $rateLimitKey,
            ttl: 300, // 5 minutes for 2FA
            rateLimitSeconds: 30 // 30 seconds rate limit
        );
    }

    /**
     * Verify 2FA OTP
     */
    public static function verify2FA(string $userId, string $otp): bool
    {
        $cacheKey = "2fa_otp:{$userId}";
        return self::verify($cacheKey, $otp);
    }

    /**
     * Generate admin verification OTP (for sensitive operations)
     */
    public static function generateAdminVerification(string $adminId, string $operation): array
    {
        $cacheKey = "admin_otp:{$adminId}:{$operation}";
        $rateLimitKey = "admin_otp_rate:{$adminId}";

        return self::generateAndStore(
            $cacheKey,
            $rateLimitKey,
            length: 8, // Longer for admin operations
            ttl: 600, // 10 minutes
            rateLimitSeconds: 60, // 1 minute rate limit
            alphanumeric: true
        );
    }

    /**
     * Verify admin verification OTP
     */
    public static function verifyAdminVerification(string $adminId, string $operation, string $otp): bool
    {
        $cacheKey = "admin_otp:{$adminId}:{$operation}";
        return self::verify($cacheKey, $otp);
    }

    /**
     * Clear all OTPs for a user (useful for security incidents)
     */
    public static function clearUserOtps(string $userId, string $email, ?string $phoneNumber = null): void
    {
        $keys = [
            "email_otp:{$email}",
            "reset_otp:{$email}",
            "2fa_otp:{$userId}",
        ];

        if ($phoneNumber) {
            $keys[] = "phone_otp:{$phoneNumber}";
        }

        foreach ($keys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Get OTP expiration time
     */
    public static function getExpirationTime(string $key): ?int
    {
        // Laravel doesn't provide direct TTL access, so we store it separately
        $ttlKey = "{$key}:ttl";
        return Cache::get($ttlKey);
    }

    /**
     * Get remaining time for rate limit
     */
    public static function getRateLimitRemaining(string $rateLimitKey): int
    {
        if (!Cache::has($rateLimitKey)) {
            return 0;
        }

        // This is an approximation since Laravel doesn't expose exact TTL
        return self::DEFAULT_RATE_LIMIT;
    }

    /**
     * Generate OTP with custom configuration
     */
    public static function generateCustom(array $config): array
    {
        $length = $config['length'] ?? self::DEFAULT_LENGTH;
        $ttl = $config['ttl'] ?? self::DEFAULT_TTL;
        $rateLimitSeconds = $config['rate_limit'] ?? self::DEFAULT_RATE_LIMIT;
        $alphanumeric = $config['alphanumeric'] ?? false;
        $cacheKey = $config['cache_key'];
        $rateLimitKey = $config['rate_limit_key'];

        return self::generateAndStore(
            $cacheKey,
            $rateLimitKey,
            $length,
            $ttl,
            $rateLimitSeconds,
            $alphanumeric
        );
    }
}
