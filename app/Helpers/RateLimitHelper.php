<?php

declare(strict_types=1);

namespace App\Helpers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

/**
 * Unified rate limiting helper
 * 
 * Consolidates rate limiting patterns across the application
 * to eliminate duplication and ensure consistency.
 */
class RateLimitHelper
{
    /**
     * Default rate limiting configurations
     */
    private const DEFAULT_ATTEMPTS = 5;
    private const DEFAULT_DECAY_MINUTES = 15;

    /**
     * Generate a rate limit key for authentication attempts
     */
    public static function getAuthKey(Request $request, string $type = 'login'): string
    {
        return "{$type}:{$request->ip()}";
    }

    /**
     * Generate a rate limit key for user-specific actions
     */
    public static function getUserKey(string $userId, string $action): string
    {
        return "{$action}:user:{$userId}";
    }

    /**
     * Generate a rate limit key for email-based actions
     */
    public static function getEmailKey(string $email, string $action): string
    {
        return "{$action}:email:" . hash('sha256', strtolower($email));
    }

    /**
     * Generate a rate limit key for phone-based actions
     */
    public static function getPhoneKey(string $phoneNumber, string $action): string
    {
        return "{$action}:phone:" . hash('sha256', $phoneNumber);
    }

    /**
     * Generate a rate limit key for IP-based actions
     */
    public static function getIpKey(Request $request, string $action): string
    {
        return "{$action}:ip:{$request->ip()}";
    }

    /**
     * Generate a rate limit key for tenant-specific actions
     */
    public static function getTenantKey(string $tenantId, string $action): string
    {
        return "{$action}:tenant:{$tenantId}";
    }

    /**
     * Check if rate limit is exceeded
     */
    public static function tooManyAttempts(
        string $key,
        int $maxAttempts = self::DEFAULT_ATTEMPTS,
        int $decayMinutes = self::DEFAULT_DECAY_MINUTES
    ): bool {
        return RateLimiter::tooManyAttempts($key, $maxAttempts);
    }

    /**
     * Hit the rate limiter
     */
    public static function hit(
        string $key,
        int $decayMinutes = self::DEFAULT_DECAY_MINUTES
    ): int {
        return RateLimiter::hit($key, $decayMinutes * 60);
    }

    /**
     * Get remaining attempts
     */
    public static function remaining(
        string $key,
        int $maxAttempts = self::DEFAULT_ATTEMPTS
    ): int {
        return RateLimiter::remaining($key, $maxAttempts);
    }

    /**
     * Get seconds until rate limit resets
     */
    public static function availableIn(string $key): int
    {
        return RateLimiter::availableIn($key);
    }

    /**
     * Clear rate limit for a key
     */
    public static function clear(string $key): void
    {
        RateLimiter::clear($key);
    }

    /**
     * Check and hit rate limit in one operation
     */
    public static function checkAndHit(
        string $key,
        int $maxAttempts = self::DEFAULT_ATTEMPTS,
        int $decayMinutes = self::DEFAULT_DECAY_MINUTES
    ): array {
        if (self::tooManyAttempts($key, $maxAttempts, $decayMinutes)) {
            return [
                'allowed' => false,
                'remaining' => 0,
                'retry_after' => self::availableIn($key),
            ];
        }

        $attempts = self::hit($key, $decayMinutes);

        return [
            'allowed' => true,
            'attempts' => $attempts,
            'remaining' => max(0, $maxAttempts - $attempts),
            'retry_after' => 0,
        ];
    }

    /**
     * Rate limit login attempts
     */
    public static function checkLoginAttempts(Request $request): array
    {
        $key = self::getAuthKey($request, 'login');
        return self::checkAndHit($key, 5, 15); // 5 attempts per 15 minutes
    }

    /**
     * Rate limit password reset attempts
     */
    public static function checkPasswordResetAttempts(string $email): array
    {
        $key = self::getEmailKey($email, 'password_reset');
        return self::checkAndHit($key, 3, 60); // 3 attempts per hour
    }

    /**
     * Rate limit email verification attempts
     */
    public static function checkEmailVerificationAttempts(string $email): array
    {
        $key = self::getEmailKey($email, 'email_verification');
        return self::checkAndHit($key, 5, 60); // 5 attempts per hour
    }

    /**
     * Rate limit phone verification attempts
     */
    public static function checkPhoneVerificationAttempts(string $phoneNumber): array
    {
        $key = self::getPhoneKey($phoneNumber, 'phone_verification');
        return self::checkAndHit($key, 3, 60); // 3 attempts per hour
    }

    /**
     * Rate limit password change attempts
     */
    public static function checkPasswordChangeAttempts(string $userId): array
    {
        $key = self::getUserKey($userId, 'password_change');
        return self::checkAndHit($key, 5, 60); // 5 attempts per hour
    }

    /**
     * Rate limit 2FA attempts
     */
    public static function check2FAAttempts(string $userId): array
    {
        $key = self::getUserKey($userId, '2fa_verification');
        return self::checkAndHit($key, 5, 30); // 5 attempts per 30 minutes
    }

    /**
     * Rate limit API requests per user
     */
    public static function checkApiAttempts(string $userId): array
    {
        $key = self::getUserKey($userId, 'api_requests');
        return self::checkAndHit($key, 1000, 60); // 1000 requests per hour
    }

    /**
     * Rate limit admin actions
     */
    public static function checkAdminActionAttempts(string $adminId, string $action): array
    {
        $key = self::getUserKey($adminId, "admin_{$action}");
        return self::checkAndHit($key, 10, 60); // 10 attempts per hour
    }

    /**
     * Rate limit tenant-specific actions
     */
    public static function checkTenantActionAttempts(string $tenantId, string $action): array
    {
        $key = self::getTenantKey($tenantId, $action);
        return self::checkAndHit($key, 100, 60); // 100 attempts per hour per tenant
    }

    /**
     * Rate limit file uploads
     */
    public static function checkFileUploadAttempts(string $userId): array
    {
        $key = self::getUserKey($userId, 'file_upload');
        return self::checkAndHit($key, 50, 60); // 50 uploads per hour
    }

    /**
     * Rate limit registration attempts from IP
     */
    public static function checkRegistrationAttempts(Request $request): array
    {
        $key = self::getIpKey($request, 'registration');
        return self::checkAndHit($key, 3, 60); // 3 registrations per hour per IP
    }

    /**
     * Rate limit contact form submissions
     */
    public static function checkContactFormAttempts(Request $request): array
    {
        $key = self::getIpKey($request, 'contact_form');
        return self::checkAndHit($key, 5, 60); // 5 submissions per hour per IP
    }

    /**
     * Clear all rate limits for a user (useful for admin override)
     */
    public static function clearUserLimits(string $userId): void
    {
        $actions = [
            'password_change',
            '2fa_verification',
            'api_requests',
            'file_upload',
        ];

        foreach ($actions as $action) {
            $key = self::getUserKey($userId, $action);
            self::clear($key);
        }
    }

    /**
     * Clear all rate limits for an email (useful for admin override)
     */
    public static function clearEmailLimits(string $email): void
    {
        $actions = [
            'password_reset',
            'email_verification',
        ];

        foreach ($actions as $action) {
            $key = self::getEmailKey($email, $action);
            self::clear($key);
        }
    }

    /**
     * Get rate limit status for debugging
     */
    public static function getStatus(string $key, int $maxAttempts = self::DEFAULT_ATTEMPTS): array
    {
        return [
            'key' => $key,
            'too_many_attempts' => self::tooManyAttempts($key, $maxAttempts),
            'remaining' => self::remaining($key, $maxAttempts),
            'available_in' => self::availableIn($key),
        ];
    }

    /**
     * Create a custom rate limit configuration
     */
    public static function createCustomLimit(
        string $key,
        int $maxAttempts,
        int $decayMinutes,
        ?callable $keyGenerator = null
    ): array {
        if ($keyGenerator) {
            $key = $keyGenerator($key);
        }

        return self::checkAndHit($key, $maxAttempts, $decayMinutes);
    }

    /**
     * Batch check multiple rate limits
     */
    public static function batchCheck(array $limits): array
    {
        $results = [];

        foreach ($limits as $name => $config) {
            $key = $config['key'];
            $maxAttempts = $config['max_attempts'] ?? self::DEFAULT_ATTEMPTS;
            $decayMinutes = $config['decay_minutes'] ?? self::DEFAULT_DECAY_MINUTES;

            $results[$name] = self::checkAndHit($key, $maxAttempts, $decayMinutes);
        }

        return $results;
    }
}
