<?php

declare(strict_types=1);

namespace App\Helpers;

/**
 * Unified validation rules helper
 * 
 * Consolidates common validation patterns across the application
 * to eliminate duplication and ensure consistency.
 */
class ValidationHelper
{
    /**
     * Get standard user name validation rules
     */
    public static function getNameRules(): array
    {
        return [
            'first_name' => 'required|string|max:255|min:2',
            'last_name' => 'required|string|max:255|min:2',
        ];
    }

    /**
     * Get email validation rules
     */
    public static function getEmailRules(bool $unique = true, ?string $ignoreId = null): array
    {
        $rule = 'required|string|email|max:255';
        
        if ($unique) {
            $rule .= '|unique:users,email';
            if ($ignoreId) {
                $rule .= ',' . $ignoreId;
            }
        }
        
        return ['email' => $rule];
    }

    /**
     * Get Nigerian phone number validation rules
     */
    public static function getPhoneRules(bool $required = true, bool $unique = true, ?string $ignoreId = null): array
    {
        $rule = ($required ? 'required' : 'nullable') . '|string|max:20';
        
        // Nigerian phone number pattern: +234XXXXXXXXXX or 0XXXXXXXXXXX
        $rule .= '|regex:/^(\+234[0-9]{10}|0[0-9]{10})$/';
        
        if ($unique) {
            $rule .= '|unique:users,phone_number';
            if ($ignoreId) {
                $rule .= ',' . $ignoreId;
            }
        }
        
        return ['phone_number' => $rule];
    }

    /**
     * Get timezone validation rules
     */
    public static function getTimezoneRules(bool $required = false): array
    {
        $rule = ($required ? 'required' : 'nullable') . '|string|max:50|timezone';
        
        return ['timezone' => $rule];
    }

    /**
     * Get UUID validation rules
     */
    public static function getUuidRules(string $field = 'id', bool $required = true): array
    {
        $rule = ($required ? 'required' : 'nullable') . '|uuid';
        
        return [$field => $rule];
    }

    /**
     * Get pagination validation rules
     */
    public static function getPaginationRules(int $maxPerPage = 100, int $defaultPerPage = 15): array
    {
        return [
            'page' => 'nullable|integer|min:1',
            'per_page' => "nullable|integer|min:1|max:{$maxPerPage}",
            'search' => 'nullable|string|max:255',
            'sort' => 'nullable|string|max:50',
            'direction' => 'nullable|string|in:asc,desc',
        ];
    }

    /**
     * Get search and filter validation rules
     */
    public static function getSearchRules(): array
    {
        return [
            'search' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:active,inactive,pending,suspended',
            'created_from' => 'nullable|date',
            'created_to' => 'nullable|date|after_or_equal:created_from',
        ];
    }

    /**
     * Get business validation rules
     */
    public static function getBusinessRules(): array
    {
        return [
            'business_name' => 'required|string|max:255|min:2|unique:tenants,name',
            'business_type' => 'required|string|max:100|in:restaurant,retail,grocery,pharmacy,logistics,other',
            'description' => 'nullable|string|max:1000',
            'website' => 'nullable|url|max:255',
        ];
    }

    /**
     * Get address validation rules
     */
    public static function getAddressRules(bool $required = true): array
    {
        $prefix = $required ? 'required' : 'nullable';
        
        return [
            'street_address' => "{$prefix}|string|max:255",
            'city' => "{$prefix}|string|max:100",
            'state' => "{$prefix}|string|max:100",
            'postal_code' => 'nullable|string|max:20',
            'country' => "{$prefix}|string|size:2", // ISO country code
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ];
    }

    /**
     * Get file upload validation rules
     */
    public static function getImageUploadRules(int $maxSizeMB = 5): array
    {
        $maxSizeKB = $maxSizeMB * 1024;
        
        return [
            'image' => "required|image|mimes:jpeg,png,jpg,gif,webp,heic,heif|max:{$maxSizeKB}",
        ];
    }

    /**
     * Get document upload validation rules
     */
    public static function getDocumentUploadRules(int $maxSizeMB = 10): array
    {
        $maxSizeKB = $maxSizeMB * 1024;
        
        return [
            'document' => "required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:{$maxSizeKB}",
        ];
    }

    /**
     * Get OTP validation rules
     */
    public static function getOtpRules(): array
    {
        return [
            'otp' => 'required|string|size:6|regex:/^[0-9]+$/',
        ];
    }

    /**
     * Get rate limiting validation rules
     */
    public static function getRateLimitRules(): array
    {
        return [
            'attempts' => 'nullable|integer|min:1|max:100',
            'decay_minutes' => 'nullable|integer|min:1|max:1440', // Max 24 hours
        ];
    }

    /**
     * Get monetary amount validation rules
     */
    public static function getMoneyRules(bool $required = true, float $min = 0, ?float $max = null): array
    {
        $rule = ($required ? 'required' : 'nullable') . '|numeric|min:' . $min;
        
        if ($max !== null) {
            $rule .= '|max:' . $max;
        }
        
        return [
            'amount' => $rule,
            'currency' => 'nullable|string|size:3|in:NGN,USD,EUR,GBP', // ISO currency codes
        ];
    }

    /**
     * Get role validation rules
     */
    public static function getRoleRules(array $allowedRoles): array
    {
        $rolesString = implode(',', $allowedRoles);
        
        return [
            'role' => "required|string|in:{$rolesString}",
        ];
    }

    /**
     * Get business role validation rules
     */
    public static function getBusinessRoleRules(): array
    {
        return self::getRoleRules(['owner', 'admin', 'manager', 'staff']);
    }

    /**
     * Get provider role validation rules
     */
    public static function getProviderRoleRules(): array
    {
        return self::getRoleRules(['owner', 'admin', 'manager', 'driver', 'staff']);
    }

    /**
     * Get admin role validation rules
     */
    public static function getAdminRoleRules(): array
    {
        return self::getRoleRules(['platform-admin', 'super-admin']);
    }

    /**
     * Get device validation rules
     */
    public static function getDeviceRules(): array
    {
        return [
            'device_name' => 'nullable|string|max:255',
            'device_type' => 'nullable|string|in:mobile,tablet,desktop,api',
            'platform' => 'nullable|string|in:ios,android,web,api',
        ];
    }

    /**
     * Get coordinates validation rules
     */
    public static function getCoordinatesRules(bool $required = true): array
    {
        $prefix = $required ? 'required' : 'nullable';
        
        return [
            'latitude' => "{$prefix}|numeric|between:-90,90",
            'longitude' => "{$prefix}|numeric|between:-180,180",
        ];
    }

    /**
     * Get date range validation rules
     */
    public static function getDateRangeRules(): array
    {
        return [
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ];
    }

    /**
     * Get status validation rules
     */
    public static function getStatusRules(array $allowedStatuses): array
    {
        $statusString = implode(',', $allowedStatuses);
        
        return [
            'status' => "required|string|in:{$statusString}",
        ];
    }

    /**
     * Get common status validation rules
     */
    public static function getCommonStatusRules(): array
    {
        return self::getStatusRules(['active', 'inactive', 'pending', 'suspended', 'verified', 'rejected']);
    }

    /**
     * Combine multiple validation rule sets
     */
    public static function combineRules(array ...$ruleSets): array
    {
        return array_merge(...$ruleSets);
    }
}
