<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Events\User\AccountSuspended;
use App\Http\Controllers\Controller;
use App\Models\User\User;
use App\Services\System\LoggingService;
use App\Services\User\AuthService;
use App\Services\Communication\NotificationService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * Central Admin User Management Controller
 *
 * Handles cross-tenant user management for platform administrators.
 * Provides comprehensive user oversight, management, and administration.
 */
class AdminUserController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly LoggingService $loggingService,
        private readonly AuthService $authService,
        private readonly NotificationService $notificationService
    ) {}

    /**
     * Get all users across tenants.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 100). Example: 15
     * @queryParam search string Search users by name or email. Example: john
     * @queryParam user_type string Filter by user type. Example: business_owner
     * @queryParam tenant_id string Filter by tenant ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @queryParam is_active boolean Filter by active status. Example: true
     * @queryParam email_verified boolean Filter by email verification. Example: true
     * @queryParam sort_by string Sort by field. Example: created_at
     * @queryParam sort_direction string Sort direction (asc, desc). Example: desc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Users retrieved successfully",
     *   "data": {
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "first_name": "John",
     *         "last_name": "Doe",
     *         "email": "<EMAIL>",
     *         "phone_number": "***********",
     *         "user_type": "business_owner",
     *         "is_active": true,
     *         "email_verified_at": "2024-01-15T10:30:00Z",
     *         "phone_verified_at": "2024-01-15T10:30:00Z",
     *         "tenant": {
     *           "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *           "name": "Example Business"
     *         },
     *         "created_at": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 150
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Build query with relationships
            $query = User::with(['tenant:id,name,tenant_type', 'roles', 'abilities']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'first_name',
                    'last_name',
                    'email',
                    'phone_number',
                    'tenant.name',
                ],
                'sortFields' => [
                    'first_name',
                    'last_name',
                    'email',
                    'user_type',
                    'created_at',
                    'updated_at',
                ],
                'filters' => [
                    'user_type' => ['type' => 'exact'],
                    'tenant_id' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                    'email_verified' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            if (filter_var($value, FILTER_VALIDATE_BOOLEAN)) {
                                $query->whereNotNull('email_verified_at');
                            } else {
                                $query->whereNull('email_verified_at');
                            }
                        },
                    ],
                ],
                'message' => 'Users retrieved successfully',
                'entityName' => 'users',
                'transformer' => [$this, 'transformUser'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve users',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'request_params' => $request->only(['search', 'user_type', 'tenant_id', 'is_active']),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve users');
        }
    }

    /**
     * Get specific user details.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "first_name": "John",
     *     "last_name": "Doe",
     *     "email": "<EMAIL>",
     *     "phone_number": "***********",
     *     "user_type": "business_owner",
     *     "is_active": true,
     *     "email_verified_at": "2024-01-15T10:30:00Z",
     *     "phone_verified_at": "2024-01-15T10:30:00Z",
     *     "tenant": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "name": "Example Business",
     *       "tenant_type": "business"
     *     },
     *     "roles": ["business-owner"],
     *     "abilities": ["manage-business"],
     *     "addresses": [],
     *     "created_at": "2024-01-15T10:30:00Z",
     *     "updated_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function show(string $user): JsonResponse
    {
        try {
            $query = User::query();

            return $this->handleShow($query, $user, [
                'with' => [
                    'tenant:id,name,tenant_type,status',
                    'roles',
                    'abilities',
                    'addresses',
                    'customerProfile',
                    'businessProfile',
                    'deliveryProviderProfile',
                ],
                'transformer' => fn ($model) => $this->transformUser($model, true),
                'message' => 'User retrieved successfully',
                'notFoundMessage' => 'User not found',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve user details',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve user details');
        }
    }

    /**
     * Update user details.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam first_name string User's first name. Example: John
     * @bodyParam last_name string User's last name. Example: Doe
     * @bodyParam email string User's email address. Example: <EMAIL>
     * @bodyParam phone_number string User's phone number. Example: ***********
     * @bodyParam is_active boolean User's active status. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "first_name": "John",
     *     "last_name": "Doe",
     *     "email": "<EMAIL>",
     *     "phone_number": "***********",
     *     "is_active": true
     *   }
     * }
     */
    public function update(Request $request, string $user): JsonResponse
    {
        $request->validate([
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|max:255|unique:users,email,'.$user,
            'phone_number' => 'sometimes|string|max:20',
            'is_active' => 'sometimes|boolean',
        ]);

        try {
            $userModel = User::findOrFail($user);

            $updateData = $request->only([
                'first_name', 'last_name', 'email', 'phone_number', 'is_active',
            ]);

            // Remove empty values
            $updateData = array_filter($updateData, function ($value) {
                return $value !== null && $value !== '';
            });

            if (empty($updateData)) {
                return $this->errorResponse(
                    'No valid data provided for update',
                    422
                );
            }

            $userModel->update($updateData);

            $this->loggingService->logInfo(
                'User updated by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                    'updated_fields' => array_keys($updateData),
                ]
            );

            return $this->successResponse(
                $this->transformUser($userModel->fresh()),
                'User updated successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'User not found',
                404
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update user',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                    'update_data' => $updateData ?? [],
                ]
            );

            return $this->serverErrorResponse('Failed to update user');
        }
    }

    /**
     * Create a new user manually (admin only).
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @bodyParam first_name string required User's first name. Example: John
     * @bodyParam last_name string required User's last name. Example: Doe
     * @bodyParam email string required User's email address. Example: <EMAIL>
     * @bodyParam phone_number string User's phone number. Example: +2348012345678
     * @bodyParam user_type string required User type. Example: customer
     * @bodyParam role string Role to assign to user. Example: customer
     * @bodyParam tenant_id string Tenant ID for business/provider users. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @bodyParam auto_verify_email boolean Auto-verify email address. Example: true
     * @bodyParam auto_verify_phone boolean Auto-verify phone number. Example: false
     * @bodyParam send_credentials boolean Send login credentials to user. Example: true
     *
     * @response 201 {
     *   "success": true,
     *   "message": "User created successfully",
     *   "data": {
     *     "user": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "first_name": "John",
     *       "last_name": "Doe",
     *       "email": "<EMAIL>",
     *       "phone_number": "+2348012345678",
     *       "user_type": "customer",
     *       "is_active": true,
     *       "email_verified_at": "2024-01-15T10:30:00Z",
     *       "phone_verified_at": null,
     *       "roles": ["customer"]
     *     },
     *     "credentials": {
     *       "email": "<EMAIL>",
     *       "temporary_password": "TempPass123!",
     *       "login_url": "https://app.deliverynexus.com/login"
     *     }
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email',
            'phone_number' => 'nullable|string|max:20',
            'user_type' => 'required|string|in:customer,business_owner,delivery_driver,platform_admin',
            'role' => 'nullable|string|max:255',
            'tenant_id' => 'nullable|uuid|exists:tenants,id',
            'auto_verify_email' => 'boolean',
            'auto_verify_phone' => 'boolean',
            'send_credentials' => 'boolean',
        ]);

        try {
            // Check if user already exists
            $existingUser = User::where('email', $request->email)->first();
            if ($existingUser) {
                return $this->errorResponse(
                    'User already exists with this email',
                    422
                );
            }

            // Generate temporary password
            $temporaryPassword = $this->generateTemporaryPassword();

            // Create user data
            $userData = [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'password' => Hash::make($temporaryPassword),
                'user_type' => $request->user_type,
                'tenant_id' => $request->tenant_id,
                'timezone' => 'Africa/Lagos',
                'is_active' => true,
                'email_verified_at' => $request->boolean('auto_verify_email') ? now() : null,
                'phone_verified_at' => $request->boolean('auto_verify_phone') && $request->phone_number ? now() : null,
            ];

            // Create user
            $user = User::create($userData);

            // Assign role if provided
            if ($request->role) {
                \Silber\Bouncer\BouncerFacade::role()->firstOrCreate(['name' => $request->role]);
                $user->assign($request->role);
            }

            // Prepare credentials for response
            $credentials = [
                'email' => $user->email,
                'temporary_password' => $temporaryPassword,
                'login_url' => config('app.frontend_url', 'https://app.deliverynexus.com') . '/login',
            ];

            // Send credentials to user if requested
            if ($request->boolean('send_credentials', true)) {
                $this->notificationService->sendUserCredentials(
                    $user,
                    $temporaryPassword,
                    auth()->user()
                );
            }

            $this->loggingService->logInfo(
                'User created manually by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'created_user_id' => $user->id,
                    'user_type' => $request->user_type,
                    'role' => $request->role,
                    'tenant_id' => $request->tenant_id,
                    'auto_verify_email' => $request->boolean('auto_verify_email'),
                    'auto_verify_phone' => $request->boolean('auto_verify_phone'),
                    'send_credentials' => $request->boolean('send_credentials', true),
                ]
            );

            return $this->successResponse(
                [
                    'user' => $this->transformUser($user->load('roles'), true),
                    'credentials' => $credentials,
                ],
                'User created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create user manually',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'request_data' => $request->except(['password']),
                ]
            );

            return $this->serverErrorResponse('Failed to create user');
        }
    }

    /**
     * Activate a user.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User activated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "is_active": true
     *   }
     * }
     */
    public function activate(string $user): JsonResponse
    {
        return $this->updateUserStatus($user, true, 'User activated successfully');
    }

    /**
     * Suspend a user.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User suspended successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "is_active": false
     *   }
     * }
     */
    public function suspend(string $user): JsonResponse
    {
        return $this->updateUserStatus($user, false, 'User suspended successfully');
    }

    /**
     * Verify user's email.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User email verified successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "email_verified_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function verifyEmail(string $user): JsonResponse
    {
        try {
            $userModel = User::findOrFail($user);

            if ($userModel->email_verified_at) {
                return $this->errorResponse(
                    'User email is already verified',
                    422
                );
            }

            $userModel->update([
                'email_verified_at' => now(),
            ]);

            $this->loggingService->logInfo(
                'User email verified by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                ]
            );

            return $this->successResponse(
                [
                    'id' => $userModel->id,
                    'email_verified_at' => $userModel->email_verified_at->toISOString(),
                ],
                'User email verified successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'User not found',
                404
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to verify user email',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                ]
            );

            return $this->serverErrorResponse('Failed to verify user email');
        }
    }

    /**
     * Verify user's phone.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User phone verified successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "phone_verified_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function verifyPhone(string $user): JsonResponse
    {
        try {
            $userModel = User::findOrFail($user);

            if ($userModel->phone_verified_at) {
                return $this->errorResponse(
                    'User phone is already verified',
                    422
                );
            }

            $userModel->update([
                'phone_verified_at' => now(),
            ]);

            $this->loggingService->logInfo(
                'User phone verified by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                ]
            );

            return $this->successResponse(
                [
                    'id' => $userModel->id,
                    'phone_verified_at' => $userModel->phone_verified_at->toISOString(),
                ],
                'User phone verified successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'User not found',
                404
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to verify user phone',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                ]
            );

            return $this->serverErrorResponse('Failed to verify user phone');
        }
    }

    // Helper Methods

    /**
     * Update user status (activate/suspend).
     */
    private function updateUserStatus(string $userId, bool $isActive, string $message): JsonResponse
    {
        try {
            $userModel = User::findOrFail($userId);

            $userModel->update(['is_active' => $isActive]);

            // Dispatch AccountSuspended event if user is being suspended
            if (! $isActive) {
                AccountSuspended::dispatch(
                    $userModel,
                    'Administrative action', // reason
                    'pending_review', // suspensionType
                    auth()->user(), // suspendedBy
                    null, // suspendedUntil
                    'Contact support for more information about your account suspension.', // appealProcess
                    ['admin_action' => true, 'admin_id' => auth()->id()] // violationDetails
                );
            }

            $this->loggingService->logInfo(
                'User status updated by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $userId,
                    'new_status' => $isActive ? 'active' : 'suspended',
                ]
            );

            return $this->successResponse(
                [
                    'id' => $userModel->id,
                    'is_active' => $userModel->is_active,
                ],
                $message
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'User not found',
                404
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update user status',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $userId,
                    'intended_status' => $isActive ? 'active' : 'suspended',
                ]
            );

            return $this->serverErrorResponse('Failed to update user status');
        }
    }

    /**
     * Transform user for API response.
     */
    private function transformUser(User $user, bool $detailed = false): array
    {
        $data = [
            'id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone_number' => $user->phone_number,
            'user_type' => $user->user_type->value,
            'is_active' => $user->is_active,
            'email_verified_at' => $user->email_verified_at?->toISOString(),
            'phone_verified_at' => $user->phone_verified_at?->toISOString(),
            'tenant' => $user->tenant ? [
                'id' => $user->tenant->id,
                'name' => $user->tenant->name,
                'tenant_type' => $user->tenant->tenant_type->value ?? null,
            ] : null,
            'created_at' => $user->created_at->toISOString(),
        ];

        if ($detailed) {
            $data = array_merge($data, [
                'roles' => $user->roles->pluck('name')->toArray(),
                'abilities' => $user->abilities->pluck('name')->toArray(),
                'addresses' => $user->addresses->map(function ($address) {
                    return [
                        'id' => $address->id,
                        'type' => $address->type,
                        'address_line_1' => $address->address_line_1,
                        'city' => $address->city,
                        'state' => $address->state,
                        'country' => $address->country,
                    ];
                })->toArray(),
                'updated_at' => $user->updated_at->toISOString(),
            ]);
        }

        return $data;
    }

    /**
     * Generate a secure temporary password.
     */
    private function generateTemporaryPassword(): string
    {
        // Generate a secure 12-character password with mixed case, numbers, and symbols
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*';

        $password = '';
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)]; // At least 1 uppercase
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)]; // At least 1 lowercase
        $password .= $numbers[random_int(0, strlen($numbers) - 1)]; // At least 1 number
        $password .= $symbols[random_int(0, strlen($symbols) - 1)]; // At least 1 symbol

        // Fill the rest with random characters
        $allChars = $uppercase . $lowercase . $numbers . $symbols;
        for ($i = 4; $i < 12; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        // Shuffle the password to randomize character positions
        return str_shuffle($password);
    }
}
