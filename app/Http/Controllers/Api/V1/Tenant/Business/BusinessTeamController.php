<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Enums\Business\TeamMemberRelationshipType;
use App\Enums\User\UserType;
use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Models\Business\BusinessTeamMember;
use App\Models\User\User;
use App\Notifications\System\SystemAnnouncement;
use App\Services\Business\BusinessService;
use App\Services\Communication\NotificationService;
use App\Services\System\LoggingService;
use App\Services\Tenant\TenantManagementService;
use App\Services\User\TeamInvitationService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

/**
 * Business Team Management Controller
 *
 * Handles tenant-scoped business team member management.
 * Manages team members, roles, and permissions within the business context.
 */
class BusinessTeamController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly BusinessService $businessService,
        private readonly LoggingService $loggingService,
        private readonly NotificationService $notificationService,
        private readonly TeamInvitationService $teamInvitationService,
        private readonly TenantManagementService $tenantManagementService
    ) {}

    /**
     * Get business team members.
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 100). Example: 15
     * @queryParam search string Search team members by name or email. Example: john
     * @queryParam role string Filter by role. Example: admin
     * @queryParam status string Filter by status (active/inactive). Example: active
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Team members retrieved successfully",
     *   "data": {
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "name": "John Doe",
     *         "email": "<EMAIL>",
     *         "is_active": true,
     *         "created_at": "2024-01-15T10:30:00Z",
     *         "roles": ["manager"]
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 5
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'search' => 'sometimes|string|max:255',
            'role' => 'sometimes|string|max:50',
            'status' => 'sometimes|string|in:active,inactive',
        ]);

        try {
            // Get current business to ensure we're in the right tenant context
            $business = $this->businessService->getCurrentBusiness();

            // Build query for team members (users in current tenant)
            $query = User::where('tenant_id', tenant()?->id);

            // Apply search filter
            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('first_name', 'LIKE', "%{$search}%")
                        ->orWhere('last_name', 'LIKE', "%{$search}%")
                        ->orWhere('email', 'LIKE', "%{$search}%");
                });
            }

            // Apply status filter
            if ($request->filled('status')) {
                $isActive = $request->input('status') === 'active';
                $query->where('is_active', $isActive);
            }

            // Apply role filter (simplified - would use Bouncer in full implementation)
            if ($request->filled('role')) {
                // For now, just filter by user type or a simple role field
                // In full implementation, this would use Bouncer's role system
                $query->where('user_type', $request->input('role'));
            }

            // Paginate results
            $perPage = min($request->input('per_page', 15), 100);
            $teamMembers = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return $this->successResponse(
                [
                    'data' => UserResource::collection($teamMembers->items()),
                    'current_page' => $teamMembers->currentPage(),
                    'per_page' => $teamMembers->perPage(),
                    'total' => $teamMembers->total(),
                    'last_page' => $teamMembers->lastPage(),
                ],
                'Team members retrieved successfully'
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve team members',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'request_params' => $request->only(['search', 'role', 'status', 'page', 'per_page']),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve team members');
        }
    }

    /**
     * Get specific team member details.
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @urlParam member string required Team member ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Team member retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Doe",
     *     "email": "<EMAIL>",
     *     "is_active": true,
     *     "created_at": "2024-01-15T10:30:00Z",
     *     "roles": ["manager"],
     *     "permissions": ["manage_orders", "view_analytics"]
     *   }
     * }
     */
    public function show(string $member): JsonResponse
    {
        try {
            // Get current business to ensure we're in the right tenant context
            $business = $this->businessService->getCurrentBusiness();

            // Find team member in current tenant
            $teamMemberRecord = BusinessTeamMember::where('id', $member)
                ->where('tenant_id', tenant()?->id)
                ->where('business_id', $business->id)
                ->with('user')
                ->first();

            if (! $teamMemberRecord) {
                return $this->notFoundResponse('Team member not found');
            }

            return $this->successResponse(
                $this->transformTeamMember($teamMemberRecord->user, $teamMemberRecord),
                'Team member retrieved successfully'
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve team member',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'member_id' => $member,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve team member');
        }
    }

    /**
     * Invite new team member.
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @bodyParam first_name string required Team member's first name. Example: John
     * @bodyParam last_name string required Team member's last name. Example: Doe
     * @bodyParam email string required Team member's email address. Example: <EMAIL>
     * @bodyParam phone_number string required Team member's phone number. Example: ***********
     * @bodyParam role string required Team member's role. Example: business-staff
     * @bodyParam password string required Team member's password. Example: password123
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Team member invited successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Doe",
     *     "email": "<EMAIL>",
     *     "phone": "***********",
     *     "role": "business-staff",
     *     "status": "active",
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone_number' => 'required|string|max:20',
            'role' => 'required|string|in:admin,manager,staff',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            // Send team invitation using TeamInvitationService
            $invitation = $this->teamInvitationService->inviteBusinessTeamMember(
                $business,
                $request->only(['first_name', 'last_name', 'email', 'phone_number', 'role']),
                auth()->user()
            );

            return $this->successResponse(
                [
                    'invitation_id' => $invitation->id,
                    'email' => $invitation->email,
                    'role' => $invitation->role,
                    'expires_at' => $invitation->expires_at->toISOString(),
                    'status' => $invitation->status,
                ],
                'Team member invitation sent successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to invite team member',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'request_data' => $request->except(['password']),
                ]
            );

            return $this->serverErrorResponse('Failed to invite team member');
        }
    }

    /**
     * Create team member directly (manual creation).
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @bodyParam first_name string required Team member's first name. Example: John
     * @bodyParam last_name string required Team member's last name. Example: Doe
     * @bodyParam email string required Team member's email address. Example: <EMAIL>
     * @bodyParam phone_number string required Team member's phone number. Example: ***********
     * @bodyParam role string required Team member's role. Example: staff
     * @bodyParam auto_verify_email boolean Auto-verify email address. Example: true
     * @bodyParam auto_verify_phone boolean Auto-verify phone number. Example: false
     * @bodyParam send_credentials boolean Send login credentials to user. Example: true
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Team member created successfully",
     *   "data": {
     *     "user": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "first_name": "John",
     *       "last_name": "Doe",
     *       "email": "<EMAIL>",
     *       "phone_number": "***********",
     *       "role": "business-staff",
     *       "is_active": true,
     *       "created_at": "2024-01-15T10:30:00Z"
     *     },
     *     "credentials": {
     *       "email": "<EMAIL>",
     *       "temporary_password": "TempPass123!",
     *       "login_url": "https://business.deliverynexus.com/login"
     *     }
     *   }
     * }
     */
    public function createDirect(Request $request): JsonResponse
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone_number' => 'required|string|max:20',
            'role' => 'required|string|in:admin,manager,staff',
            'auto_verify_email' => 'boolean',
            'auto_verify_phone' => 'boolean',
            'send_credentials' => 'boolean',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            // Check if user already exists
            $existingUser = User::where('email', $request->email)->first();
            if ($existingUser) {
                return $this->errorResponse(
                    'User already exists with this email',
                    422
                );
            }

            // Use TenantManagementService to create staff member
            $result = $this->tenantManagementService->createStaffMember(
                $business,
                $request->only(['first_name', 'last_name', 'email', 'phone_number', 'role']),
                auth()->user(),
                $request->boolean('auto_verify_email'),
                $request->boolean('auto_verify_phone')
            );

            $user = $result['user'];
            $credentials = $result['credentials'];

            // Get team member record for response
            $teamMember = BusinessTeamMember::where('business_id', $business->id)
                ->where('user_id', $user->id)
                ->first();

            $this->loggingService->logInfo(
                'Business team member created directly',
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id,
                    'created_user_id' => $user->id,
                    'created_by' => auth()->id(),
                    'role' => $request->role,
                    'auto_verify_email' => $request->boolean('auto_verify_email'),
                    'auto_verify_phone' => $request->boolean('auto_verify_phone'),
                    'send_credentials' => $request->boolean('send_credentials', true),
                ]
            );

            return $this->successResponse(
                [
                    'user' => $this->transformTeamMember($user, $teamMember),
                    'credentials' => $credentials,
                ],
                'Team member created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create team member directly',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'request_data' => $request->except(['password']),
                ]
            );

            return $this->serverErrorResponse('Failed to create team member');
        }
    }

    /**
     * Update team member.
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @bodyParam first_name string Team member's first name. Example: John
     * @bodyParam last_name string Team member's last name. Example: Doe
     * @bodyParam phone_number string Team member's phone number. Example: ***********
     * @bodyParam is_active boolean Team member's active status. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Team member updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Doe",
     *     "email": "<EMAIL>",
     *     "phone": "***********",
     *     "role": "business-staff",
     *     "status": "active",
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function update(Request $request, string $member): JsonResponse
    {
        $request->validate([
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'phone_number' => 'sometimes|string|max:20',
            'is_active' => 'sometimes|boolean',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            // Find team member in current tenant
            $teamMemberRecord = BusinessTeamMember::where('id', $member)
                ->where('tenant_id', tenant()?->id)
                ->where('business_id', $business->id)
                ->with('user')
                ->first();

            if (! $teamMemberRecord) {
                return $this->notFoundResponse('Team member not found');
            }

            $user = $teamMemberRecord->user;

            // Update team member user
            $updateData = $request->only([
                'first_name', 'last_name', 'phone_number', 'is_active',
            ]);

            if (! empty($updateData)) {
                $user->update($updateData);
            }

            $this->loggingService->logInfo(
                'Team member updated successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id,
                    'team_member_id' => $teamMemberRecord->id,
                    'user_id' => auth()->id(),
                    'updated_user_id' => $user->id,
                    'updated_fields' => array_keys($updateData),
                ]
            );

            return $this->successResponse(
                $this->transformTeamMember($user, $teamMemberRecord),
                'Team member updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update team member',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'member_id' => $member,
                ]
            );

            return $this->serverErrorResponse('Failed to update team member');
        }
    }

    /**
     * Remove team member.
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Team member removed successfully"
     * }
     */
    public function destroy(string $member): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Find team member in current tenant
            $teamMemberRecord = BusinessTeamMember::where('id', $member)
                ->where('tenant_id', tenant()?->id)
                ->where('business_id', $business->id)
                ->with('user')
                ->first();

            if (! $teamMemberRecord) {
                return $this->notFoundResponse('Team member not found');
            }

            // Remove the team member relationship (soft delete)
            $teamMemberRecord->delete();

            // Also deactivate the user if they have no other business relationships
            $user = $teamMemberRecord->user;
            $otherBusinessMemberships = BusinessTeamMember::where('user_id', $user->id)
                ->where('id', '!=', $teamMemberRecord->id)
                ->exists();

            if (! $otherBusinessMemberships) {
                $user->update(['is_active' => false]);
            }

            $this->loggingService->logInfo(
                'Team member removed successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id,
                    'team_member_id' => $teamMemberRecord->id,
                    'user_id' => auth()->id(),
                ]
            );

            return $this->successResponse(
                null,
                'Team member removed successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to remove team member',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'member_id' => $member,
                ]
            );

            return $this->serverErrorResponse('Failed to remove team member');
        }
    }

    /**
     * Assign role to team member (placeholder).
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @response 501 {
     *   "success": false,
     *   "message": "Role assignment not implemented yet"
     * }
     */
    public function assignRole(Request $request, string $member): JsonResponse
    {
        return $this->errorResponse(
            'Role assignment not implemented yet',
            501
        );
    }

    /**
     * Remove role from team member (placeholder).
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @response 501 {
     *   "success": false,
     *   "message": "Role removal not implemented yet"
     * }
     */
    public function removeRole(Request $request, string $member): JsonResponse
    {
        return $this->errorResponse(
            'Role removal not implemented yet',
            501
        );
    }

    /**
     * Activate team member.
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @urlParam member string required Team member ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Team member activated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Doe",
     *     "email": "<EMAIL>",
     *     "phone": "***********",
     *     "role": "staff",
     *     "is_active": true,
     *     "joined_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function activate(string $member): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Find team member in current tenant
            $teamMemberRecord = BusinessTeamMember::where('id', $member)
                ->where('tenant_id', tenant()?->id)
                ->where('business_id', $business->id)
                ->with('user')
                ->first();

            if (! $teamMemberRecord) {
                return $this->notFoundResponse('Team member not found');
            }

            $user = $teamMemberRecord->user;

            // Check if already active
            if ($user->is_active) {
                return $this->errorResponse('Team member is already active', 422);
            }

            // Use TenantManagementService to change status
            $this->tenantManagementService->changeTeamMemberStatus(
                $user,
                $business,
                'activated',
                auth()->user(),
                null,
                'Your account has been activated. You can now access the business dashboard.'
            );

            return $this->successResponse(
                $this->transformTeamMember($user, $teamMemberRecord),
                'Team member activated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to activate team member',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'member_id' => $member,
                ]
            );

            return $this->serverErrorResponse('Failed to activate team member');
        }
    }

    /**
     * Deactivate team member.
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @urlParam member string required Team member ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Team member deactivated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Doe",
     *     "email": "<EMAIL>",
     *     "phone": "***********",
     *     "role": "staff",
     *     "is_active": false,
     *     "joined_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function deactivate(string $member): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Find team member in current tenant
            $teamMemberRecord = BusinessTeamMember::where('id', $member)
                ->where('tenant_id', tenant()?->id)
                ->where('business_id', $business->id)
                ->with('user')
                ->first();

            if (! $teamMemberRecord) {
                return $this->notFoundResponse('Team member not found');
            }

            $user = $teamMemberRecord->user;

            // Check if already inactive
            if (! $user->is_active) {
                return $this->errorResponse('Team member is already inactive', 422);
            }

            // Prevent deactivating business owner
            if ($user->isA('business-owner')) {
                return $this->errorResponse('Cannot deactivate business owner', 422);
            }

            // Revoke all active tokens for the user
            $user->tokens()->delete();

            // Use TenantManagementService to change status
            $this->tenantManagementService->changeTeamMemberStatus(
                $user,
                $business,
                'deactivated',
                auth()->user(),
                null,
                'Your access to the business dashboard has been suspended.'
            );

            return $this->successResponse(
                $this->transformTeamMember($user, $teamMemberRecord),
                'Team member deactivated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to deactivate team member',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'member_id' => $member,
                ]
            );

            return $this->serverErrorResponse('Failed to deactivate team member');
        }
    }

    /**
     * Get business admins.
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Business admins retrieved successfully",
     *   "data": [
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "name": "John Owner",
     *       "email": "<EMAIL>",
     *       "phone": "***********",
     *       "role": "business-owner",
     *       "is_active": true,
     *       "joined_at": "2024-01-01T00:00:00Z"
     *     },
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd88b",
     *       "name": "Jane Admin",
     *       "email": "<EMAIL>",
     *       "phone": "***********",
     *       "role": "business-admin",
     *       "is_active": true,
     *       "joined_at": "2024-01-15T10:30:00Z"
     *     }
     *   ]
     * }
     */
    public function getAdmins(): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Get all team members and filter for admin roles
            $adminRoles = ['business-owner', 'business-admin', 'business-manager'];

            $teamMembers = BusinessTeamMember::where('tenant_id', tenant()?->id)
                ->where('business_id', $business->id)
                ->with('user')
                ->get();

            $admins = $teamMembers
                ->filter(function ($teamMember) use ($adminRoles) {
                    if (! $teamMember->user) {
                        return false;
                    }

                    // Check if user has any admin role
                    foreach ($adminRoles as $role) {
                        if ($teamMember->user->isA($role)) {
                            return true;
                        }
                    }

                    return false;
                })
                ->map(function ($teamMember) {
                    return $this->transformTeamMember($teamMember->user, $teamMember);
                })
                ->values();

            $this->loggingService->logInfo(
                'Business admins retrieved successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id,
                    'admin_count' => $admins->count(),
                    'requested_by' => auth()->id(),
                ]
            );

            return $this->successResponse(
                $admins,
                'Business admins retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve business admins',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve business admins');
        }
    }

    /**
     * Assign admin role.
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @bodyParam user_id string required User ID to assign admin role. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @bodyParam role string required Admin role to assign. Example: business-admin
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Admin role assigned successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Jane Doe",
     *     "email": "<EMAIL>",
     *     "phone": "***********",
     *     "role": "business-admin",
     *     "is_active": true,
     *     "joined_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function assignAdmin(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|string|exists:users,id',
            'role' => 'required|string|in:business-admin,business-manager',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();
            $userId = $request->input('user_id');
            $role = $request->input('role');

            // Find team member in current tenant
            $teamMemberRecord = BusinessTeamMember::where('tenant_id', tenant()?->id)
                ->where('business_id', $business->id)
                ->whereHas('user', function ($query) use ($userId) {
                    $query->where('id', $userId);
                })
                ->with('user')
                ->first();

            if (! $teamMemberRecord) {
                return $this->notFoundResponse('Team member not found in this business');
            }

            $user = $teamMemberRecord->user;

            // Check if user already has an admin role
            if ($user->isAn('business-owner', 'business-admin', 'business-manager')) {
                return $this->errorResponse('User already has an admin role', 422);
            }

            // Assign the admin role
            $user->assign($role);

            $this->loggingService->logInfo(
                'Admin role assigned successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id,
                    'team_member_id' => $teamMemberRecord->id,
                    'assigned_user_id' => $user->id,
                    'assigned_role' => $role,
                    'assigned_by' => auth()->id(),
                ]
            );

            // Dispatch event instead of calling notification service directly
            \App\Events\Business\TeamMemberStatusChanged::dispatch(
                $user,
                $business,
                auth()->user(),
                'role_assigned',
                $role,
                "You have been assigned the {$role} role for {$business->business_name}."
            );

            return $this->successResponse(
                $this->transformTeamMember($user, $teamMemberRecord),
                'Admin role assigned successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to assign admin role',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'target_user_id' => $request->input('user_id'),
                    'role' => $request->input('role'),
                ]
            );

            return $this->serverErrorResponse('Failed to assign admin role');
        }
    }

    /**
     * Transfer ownership.
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @bodyParam new_owner_id string required User ID of the new owner. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @bodyParam confirmation_password string required Current owner's password for confirmation. Example: current_password
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Ownership transferred successfully",
     *   "data": {
     *     "old_owner": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd88a",
     *       "name": "John Owner",
     *       "role": "business-admin"
     *     },
     *     "new_owner": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "name": "Jane Admin",
     *       "role": "business-owner"
     *     }
     *   }
     * }
     */
    public function transferOwnership(Request $request): JsonResponse
    {
        $request->validate([
            'new_owner_id' => 'required|string|exists:users,id',
            'confirmation_password' => 'required|string|min:8',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();
            $currentUser = auth()->user();
            $newOwnerId = $request->input('new_owner_id');

            // Verify current user is the business owner
            if (! $currentUser->isA('business-owner')) {
                return $this->errorResponse('Only business owner can transfer ownership', 403);
            }

            // Verify password
            if (! \Hash::check($request->input('confirmation_password'), $currentUser->password)) {
                return $this->errorResponse('Invalid password confirmation', 422);
            }

            // Find new owner team member record
            $newOwnerTeamRecord = BusinessTeamMember::where('tenant_id', tenant()?->id)
                ->where('business_id', $business->id)
                ->whereHas('user', function ($query) use ($newOwnerId) {
                    $query->where('id', $newOwnerId);
                })
                ->with('user')
                ->first();

            if (! $newOwnerTeamRecord) {
                return $this->notFoundResponse('New owner must be a team member of this business');
            }

            $newOwner = $newOwnerTeamRecord->user;

            // Prevent transferring to self
            if ($newOwner->id === $currentUser->id) {
                return $this->errorResponse('Cannot transfer ownership to yourself', 422);
            }

            // Ensure new owner is active
            if (! $newOwner->is_active) {
                return $this->errorResponse('New owner must be an active team member', 422);
            }

            \DB::transaction(function () use ($currentUser, $newOwner, $business) {
                // Remove business-owner role from current owner and assign business-admin
                $currentUser->retract('business-owner');
                $currentUser->assign('business-admin');

                // Remove any existing admin roles from new owner and assign business-owner
                $newOwner->retract(['business-admin', 'business-manager']);
                $newOwner->assign('business-owner');

                // Update business owner_id if the field exists
                if (\Schema::hasColumn('businesses', 'owner_id')) {
                    $business->update(['owner_id' => $newOwner->id]);
                }
            });

            $this->loggingService->logInfo(
                'Business ownership transferred successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id,
                    'old_owner_id' => $currentUser->id,
                    'new_owner_id' => $newOwner->id,
                    'transferred_at' => now()->toISOString(),
                ]
            );

            // Dispatch events instead of calling notification service directly
            \App\Events\Business\TeamMemberStatusChanged::dispatch(
                $newOwner,
                $business,
                $currentUser,
                'ownership_transferred',
                'business-owner',
                "You are now the owner of {$business->business_name}."
            );

            \App\Events\Business\TeamMemberStatusChanged::dispatch(
                $currentUser,
                $business,
                $currentUser,
                'ownership_transferred',
                'business-admin',
                "You have transferred ownership of {$business->business_name} to {$newOwner->first_name} {$newOwner->last_name}."
            );

            return $this->successResponse(
                [
                    'old_owner' => [
                        'id' => $currentUser->id,
                        'name' => $currentUser->first_name.' '.$currentUser->last_name,
                        'role' => 'business-admin',
                    ],
                    'new_owner' => [
                        'id' => $newOwner->id,
                        'name' => $newOwner->first_name.' '.$newOwner->last_name,
                        'role' => 'business-owner',
                    ],
                ],
                'Ownership transferred successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to transfer ownership',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'new_owner_id' => $request->input('new_owner_id'),
                ]
            );

            return $this->serverErrorResponse('Failed to transfer ownership');
        }
    }

    /**
     * Update admin role (placeholder).
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @response 501 {
     *   "success": false,
     *   "message": "Admin role update not implemented yet"
     * }
     */
    public function updateAdminRole(Request $request, string $user): JsonResponse
    {
        return $this->errorResponse(
            'Admin role update not implemented yet',
            501
        );
    }

    /**
     * Remove admin (placeholder).
     *
     * @group Business Team
     *
     * @authenticated
     *
     * @response 501 {
     *   "success": false,
     *   "message": "Admin removal not implemented yet"
     * }
     */
    public function removeAdmin(string $user): JsonResponse
    {
        return $this->errorResponse(
            'Admin removal not implemented yet',
            501
        );
    }

    // Helper Methods

    /**
     * Transform team member for API response.
     */
    private function transformTeamMember(User $user, ?BusinessTeamMember $teamMember = null): array
    {
        return [
            'id' => $user->id,
            'team_member_id' => $teamMember?->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'name' => $user->first_name.' '.$user->last_name,
            'email' => $user->email,
            'phone' => $user->phone_number,
            'role' => $teamMember?->role->value ?? 'staff',
            'status' => $user->is_active ? 'active' : 'inactive',
            'is_active' => $user->is_active,
            'joined_at' => $teamMember?->created_at->toISOString() ?? $user->created_at->toISOString(),
            'created_at' => $teamMember?->created_at->toISOString() ?? $user->created_at->toISOString(),
        ];
    }

    /**
     * Generate a secure temporary password.
     */
    private function generateTemporaryPassword(): string
    {
        // Generate a secure 12-character password with mixed case, numbers, and symbols
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*';

        $password = '';
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)]; // At least 1 uppercase
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)]; // At least 1 lowercase
        $password .= $numbers[random_int(0, strlen($numbers) - 1)]; // At least 1 number
        $password .= $symbols[random_int(0, strlen($symbols) - 1)]; // At least 1 symbol

        // Fill the rest with random characters
        $allChars = $uppercase . $lowercase . $numbers . $symbols;
        for ($i = 4; $i < 12; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        // Shuffle the password to randomize character positions
        return str_shuffle($password);
    }
}
