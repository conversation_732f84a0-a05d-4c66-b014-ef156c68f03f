<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Provider;

use App\Enums\Business\TeamMemberRelationshipType;
use App\Enums\Delivery\ProviderAvailabilityStatus;
use App\Enums\User\UserType;
use App\Http\Controllers\Controller;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Delivery\ProviderTeamMember;
use App\Models\User\User;
use App\Services\Communication\NotificationService;
use App\Services\System\LoggingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

/**
 * Provider Team Management Controller
 *
 * Handles team member management for delivery providers including
 * staff, drivers, and other team members.
 */
class ProviderTeamController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private readonly LoggingService $loggingService,
        private readonly NotificationService $notificationService
    ) {}

    /**
     * Get provider team members.
     *
     * @group Provider Team
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 50). Example: 15
     * @queryParam search string Search team members by name or email. Example: john
     * @queryParam role string Filter by role (driver, dispatcher, manager). Example: driver
     * @queryParam is_active boolean Filter by active status. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Team members retrieved successfully",
     *   "data": {
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "name": "John Driver",
     *         "email": "<EMAIL>",
     *         "phone": "08012345678",
     *         "role": "driver",
     *         "is_active": true,
     *         "is_available": true,
     *         "total_deliveries": 150,
     *         "rating": 4.7,
     *         "joined_at": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 10
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            // For now, return placeholder data using a mock query
            // In real implementation, this would query actual team members
            $placeholderData = collect([
                (object) [
                    'id' => '019723aa-3202-70dd-a0c1-3565681dd87a',
                    'name' => 'John Driver',
                    'email' => '<EMAIL>',
                    'phone' => '08012345678',
                    'role' => 'driver',
                    'is_active' => true,
                    'is_available' => true,
                    'total_deliveries' => 150,
                    'rating' => 4.7,
                    'joined_at' => '2024-01-15T10:30:00Z',
                ],
                (object) [
                    'id' => '019723aa-3202-70dd-a0c1-3565681dd88b',
                    'name' => 'Jane Dispatcher',
                    'email' => '<EMAIL>',
                    'phone' => '08012345679',
                    'role' => 'dispatcher',
                    'is_active' => true,
                    'is_available' => true,
                    'total_deliveries' => 0,
                    'rating' => 0,
                    'joined_at' => '2024-01-10T08:15:00Z',
                ],
                (object) [
                    'id' => '019723aa-3202-70dd-a0c1-3565681dd89c',
                    'name' => 'Mike Manager',
                    'email' => '<EMAIL>',
                    'phone' => '08012345680',
                    'role' => 'manager',
                    'is_active' => true,
                    'is_available' => true,
                    'total_deliveries' => 0,
                    'rating' => 0,
                    'joined_at' => '2024-01-05T12:00:00Z',
                ],
            ]);

            // Apply manual filtering for placeholder data
            $filtered = $this->applyPlaceholderFilters($placeholderData, $request);

            // Manual pagination for placeholder data
            $perPage = min($request->input('per_page', 15), 50);
            $page = $request->input('page', 1);
            $total = $filtered->count();
            $offset = ($page - 1) * $perPage;
            $paginatedMembers = $filtered->slice($offset, $perPage)->values();

            return $this->successResponse(
                [
                    'data' => $paginatedMembers->map(fn ($member) => (array) $member),
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                ],
                'Team members retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->serverErrorResponse('Failed to retrieve team members');
        }
    }

    /**
     * Get specific team member details.
     *
     * @group Provider Team
     *
     * @authenticated
     *
     * @urlParam member string required Team member ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Team member retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Driver",
     *     "email": "<EMAIL>",
     *     "phone": "08012345678",
     *     "role": "driver",
     *     "is_active": true,
     *     "is_available": true,
     *     "total_deliveries": 150,
     *     "completed_deliveries": 145,
     *     "rating": 4.7,
     *     "vehicle_info": {
     *       "type": "motorcycle",
     *       "plate_number": "ABC123KD",
     *       "model": "Honda CB150"
     *     },
     *     "joined_at": "2024-01-15T10:30:00Z",
     *     "last_active": "2024-01-20T14:30:00Z"
     *   }
     * }
     */
    public function show(string $member): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();
        } catch (\Exception $e) {
            return $this->notFoundResponse('Provider not found');
        }

        try {
            // Find team member in current tenant
            $teamMemberRecord = ProviderTeamMember::where('id', $member)
                ->where('tenant_id', tenant()?->id)
                ->where('provider_id', $provider->id)
                ->with('user')
                ->first();

            if (! $teamMemberRecord) {
                return $this->notFoundResponse('Team member not found');
            }

            $user = $teamMemberRecord->user;

            // Get additional statistics (placeholder for now)
            $memberData = $this->transformTeamMember($user, $teamMemberRecord);
            $memberData['total_deliveries'] = 0; // TODO: Implement when delivery system is ready
            $memberData['completed_deliveries'] = 0;
            $memberData['rating'] = 0.0;
            $memberData['vehicle_info'] = null; // TODO: Implement vehicle management
            $memberData['last_active'] = $user->updated_at->toISOString();

            return $this->successResponse(
                $memberData,
                'Team member retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve provider team member details',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'member_id' => $member,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve team member details');
        }
    }

    /**
     * Add team member.
     *
     * @group Provider Team
     *
     * @authenticated
     *
     * @bodyParam first_name string required Team member's first name. Example: John
     * @bodyParam last_name string required Team member's last name. Example: Driver
     * @bodyParam email string required Team member email. Example: <EMAIL>
     * @bodyParam phone_number string required Team member phone. Example: 08012345678
     * @bodyParam role string required Team member role. Example: driver
     * @bodyParam password string required Team member's password. Example: password123
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Team member invited successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Driver",
     *     "email": "<EMAIL>",
     *     "phone": "08012345678",
     *     "role": "driver",
     *     "status": "active",
     *     "availability_status": "offline",
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone_number' => 'required|string|max:20',
            'role' => 'required|string|in:admin,manager,driver,support',
            'password' => 'required|string|min:8',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Create team member user account
            $user = User::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'password' => Hash::make($request->password),
                'user_type' => UserType::DELIVERY_PARTNER,
                'tenant_id' => tenant()?->id,
                'is_active' => true,
                'email_verified_at' => now(), // Auto-verify for provider-added staff
            ]);

            // Create provider team member relationship
            $teamMember = ProviderTeamMember::create([
                'tenant_id' => tenant()?->id,
                'provider_id' => $provider->id,
                'user_id' => $user->id,
                'role' => TeamMemberRelationshipType::from($request->role),
                'availability_status' => ProviderAvailabilityStatus::OFFLINE,
            ]);

            // Assign role using Bouncer based on the team member role
            $bouncerRole = match ($request->role) {
                'admin' => 'delivery-provider-admin',
                'manager' => 'delivery-manager',
                'driver' => 'delivery-driver',
                'support' => 'delivery-support',
                default => 'delivery-driver'
            };
            $user->assign($bouncerRole);

            // Dispatch event instead of calling notification service directly
            \App\Events\Delivery\ProviderStaffAdded::dispatch(
                $user,
                $provider,
                auth()->user(),
                $request->role,
                $request->password // Pass the original password for credentials
            );

            $this->loggingService->logInfo(
                'Provider team member invited successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'team_member_id' => $teamMember->id,
                    'user_id' => auth()->id(),
                    'invited_user_id' => $user->id,
                    'role' => $request->role,
                ]
            );

            return $this->successResponse(
                $this->transformTeamMember($user, $teamMember),
                'Team member invited successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to invite provider team member',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'request_data' => $request->except(['password']),
                ]
            );

            return $this->serverErrorResponse('Failed to invite team member');
        }
    }

    /**
     * Create team member directly (manual creation).
     *
     * @group Provider Team
     *
     * @authenticated
     *
     * @bodyParam first_name string required Team member's first name. Example: John
     * @bodyParam last_name string required Team member's last name. Example: Doe
     * @bodyParam email string required Team member's email address. Example: <EMAIL>
     * @bodyParam phone_number string required Team member's phone number. Example: 08012345678
     * @bodyParam role string required Team member's role. Example: driver
     * @bodyParam auto_verify_email boolean Auto-verify email address. Example: true
     * @bodyParam auto_verify_phone boolean Auto-verify phone number. Example: false
     * @bodyParam send_credentials boolean Send login credentials to user. Example: true
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Team member created successfully",
     *   "data": {
     *     "user": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "first_name": "John",
     *       "last_name": "Doe",
     *       "email": "<EMAIL>",
     *       "phone_number": "08012345678",
     *       "role": "delivery-driver",
     *       "is_active": true,
     *       "created_at": "2024-01-15T10:30:00Z"
     *     },
     *     "credentials": {
     *       "email": "<EMAIL>",
     *       "temporary_password": "TempPass123!",
     *       "login_url": "https://provider.deliverynexus.com/login"
     *     }
     *   }
     * }
     */
    public function createDirect(Request $request): JsonResponse
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone_number' => 'required|string|max:20',
            'role' => 'required|string|in:admin,manager,driver',
            'auto_verify_email' => 'boolean',
            'auto_verify_phone' => 'boolean',
            'send_credentials' => 'boolean',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Check if user already exists
            $existingUser = User::where('email', $request->email)->first();
            if ($existingUser) {
                return $this->errorResponse(
                    'User already exists with this email',
                    422
                );
            }

            // Generate temporary password
            $temporaryPassword = $this->generateTemporaryPassword();

            // Create user
            $user = User::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'password' => Hash::make($temporaryPassword),
                'user_type' => UserType::DELIVERY_DRIVER, // Will be refined by role
                'tenant_id' => tenant()?->id,
                'timezone' => 'Africa/Lagos',
                'is_active' => true,
                'email_verified_at' => $request->boolean('auto_verify_email') ? now() : null,
                'phone_verified_at' => $request->boolean('auto_verify_phone') && $request->phone_number ? now() : null,
            ]);

            // Create provider team member relationship
            $teamMember = ProviderTeamMember::create([
                'tenant_id' => tenant()?->id,
                'provider_id' => $provider->id,
                'user_id' => $user->id,
                'role' => TeamMemberRelationshipType::from($request->role),
                'joined_at' => now(),
            ]);

            // Assign role using Bouncer
            $bouncerRole = match ($request->role) {
                'admin' => 'provider-admin',
                'manager' => 'provider-manager',
                'driver' => 'delivery-driver',
                default => 'delivery-driver'
            };
            $user->assign($bouncerRole);

            // Prepare credentials for response
            $credentials = [
                'email' => $user->email,
                'temporary_password' => $temporaryPassword,
                'login_url' => config('app.tenant_url', 'https://provider.deliverynexus.com') . '/login',
            ];

            // Dispatch event instead of calling notification service directly
            \App\Events\Delivery\ProviderStaffAdded::dispatch(
                $user,
                $provider,
                auth()->user(),
                $request->role,
                $temporaryPassword
            );

            $this->loggingService->logInfo(
                'Provider team member created directly',
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'created_user_id' => $user->id,
                    'created_by' => auth()->id(),
                    'role' => $request->role,
                    'auto_verify_email' => $request->boolean('auto_verify_email'),
                    'auto_verify_phone' => $request->boolean('auto_verify_phone'),
                    'send_credentials' => $request->boolean('send_credentials', true),
                ]
            );

            return $this->successResponse(
                [
                    'user' => $this->transformTeamMember($user, $teamMember),
                    'credentials' => $credentials,
                ],
                'Team member created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create team member directly',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'request_data' => $request->except(['password']),
                ]
            );

            return $this->serverErrorResponse('Failed to create team member');
        }
    }

    /**
     * Update team member.
     *
     * @group Provider Team
     *
     * @authenticated
     *
     * @urlParam member string required Team member ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam first_name string Team member's first name. Example: John
     * @bodyParam last_name string Team member's last name. Example: Driver
     * @bodyParam phone_number string Team member phone. Example: 08012345678
     * @bodyParam role string Team member role. Example: driver
     * @bodyParam availability_status string Availability status. Example: available
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Team member updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Driver",
     *     "email": "<EMAIL>",
     *     "phone": "08012345678",
     *     "role": "driver",
     *     "status": "active",
     *     "availability_status": "available",
     *     "updated_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function update(Request $request, string $member): JsonResponse
    {
        $request->validate([
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'phone_number' => 'sometimes|string|max:20',
            'role' => 'sometimes|string|in:admin,manager,driver,support',
            'availability_status' => 'sometimes|string|in:available,busy,offline',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Find team member in current tenant
            $teamMemberRecord = ProviderTeamMember::where('id', $member)
                ->where('tenant_id', tenant()?->id)
                ->where('provider_id', $provider->id)
                ->with('user')
                ->first();

            if (! $teamMemberRecord) {
                return $this->notFoundResponse('Team member not found');
            }

            $user = $teamMemberRecord->user;

            // Update user information
            $userUpdates = [];
            if ($request->filled('first_name')) {
                $userUpdates['first_name'] = $request->first_name;
            }
            if ($request->filled('last_name')) {
                $userUpdates['last_name'] = $request->last_name;
            }
            if ($request->filled('phone_number')) {
                $userUpdates['phone_number'] = $request->phone_number;
            }

            if (! empty($userUpdates)) {
                $user->update($userUpdates);
            }

            // Update team member information
            $teamMemberUpdates = [];
            if ($request->filled('role')) {
                $oldRole = $teamMemberRecord->role->value;
                $newRole = $request->role;

                if ($oldRole !== $newRole) {
                    $teamMemberUpdates['role'] = TeamMemberRelationshipType::from($newRole);

                    // Update Bouncer role
                    $oldBouncerRole = match ($oldRole) {
                        'admin' => 'delivery-provider-admin',
                        'manager' => 'delivery-manager',
                        'driver' => 'delivery-driver',
                        'support' => 'delivery-support',
                        default => 'delivery-driver'
                    };

                    $newBouncerRole = match ($newRole) {
                        'admin' => 'delivery-provider-admin',
                        'manager' => 'delivery-manager',
                        'driver' => 'delivery-driver',
                        'support' => 'delivery-support',
                        default => 'delivery-driver'
                    };

                    $user->retract($oldBouncerRole);
                    $user->assign($newBouncerRole);
                }
            }

            if ($request->filled('availability_status')) {
                $teamMemberUpdates['availability_status'] = ProviderAvailabilityStatus::from($request->availability_status);
            }

            if (! empty($teamMemberUpdates)) {
                $teamMemberRecord->update($teamMemberUpdates);
            }

            $this->loggingService->logInfo(
                'Provider team member updated successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'team_member_id' => $teamMemberRecord->id,
                    'updated_by' => auth()->id(),
                    'updates' => array_merge($userUpdates, $teamMemberUpdates),
                ]
            );

            return $this->successResponse(
                $this->transformTeamMember($user->fresh(), $teamMemberRecord->fresh()),
                'Team member updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update provider team member',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'member_id' => $member,
                ]
            );

            return $this->serverErrorResponse('Failed to update team member');
        }
    }

    /**
     * Remove team member.
     *
     * @group Provider Team
     *
     * @authenticated
     *
     * @urlParam member string required Team member ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Team member removed successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Driver",
     *     "email": "<EMAIL>",
     *     "removed_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function destroy(string $member): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            // Find team member in current tenant
            $teamMemberRecord = ProviderTeamMember::where('id', $member)
                ->where('tenant_id', tenant()?->id)
                ->where('provider_id', $provider->id)
                ->with('user')
                ->first();

            if (! $teamMemberRecord) {
                return $this->notFoundResponse('Team member not found');
            }

            $user = $teamMemberRecord->user;

            // Prevent removing provider owner
            if ($user->isA('delivery-provider-owner')) {
                return $this->errorResponse('Cannot remove provider owner', 422);
            }

            // Check if user has active deliveries
            $hasActiveDeliveries = false; // TODO: Check for active deliveries when delivery system is implemented

            if ($hasActiveDeliveries) {
                return $this->errorResponse('Cannot remove team member with active deliveries', 422);
            }

            \DB::transaction(function () use ($teamMemberRecord, $user) {
                // Remove team member relationship
                $teamMemberRecord->delete();

                // Deactivate user account (don't delete to preserve audit trail)
                $user->update(['is_active' => false]);

                // Revoke all tokens
                $user->tokens()->delete();

                // Remove all roles
                $user->retract($user->getRoles());
            });

            // Dispatch event instead of calling notification service directly
            \App\Events\Delivery\ProviderTeamMemberStatusChanged::dispatch(
                $user,
                $provider,
                auth()->user(),
                'removed',
                null,
                "Your membership with {$provider->company_name} has been ended."
            );

            $this->loggingService->logInfo(
                'Provider team member removed successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'team_member_id' => $teamMemberRecord->id,
                    'removed_user_id' => $user->id,
                    'removed_by' => auth()->id(),
                ]
            );

            return $this->successResponse(
                [
                    'id' => $user->id,
                    'name' => $user->first_name.' '.$user->last_name,
                    'email' => $user->email,
                    'removed_at' => now()->toISOString(),
                ],
                'Team member removed successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to remove provider team member',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'member_id' => $member,
                ]
            );

            return $this->serverErrorResponse('Failed to remove team member');
        }
    }

    // Helper Methods

    /**
     * Apply filters to placeholder data.
     */
    private function applyPlaceholderFilters($data, $request)
    {
        if ($request->filled('search')) {
            $search = strtolower($request->input('search'));
            $data = $data->filter(function ($member) use ($search) {
                return str_contains(strtolower($member->name), $search) ||
                       str_contains(strtolower($member->email), $search);
            });
        }

        if ($request->filled('role')) {
            $role = $request->input('role');
            $data = $data->filter(function ($member) use ($role) {
                return $member->role === $role;
            });
        }

        if ($request->has('is_active')) {
            $isActive = $request->boolean('is_active');
            $data = $data->filter(function ($member) use ($isActive) {
                return $member->is_active === $isActive;
            });
        }

        return $data;
    }

    /**
     * Get current provider.
     */
    private function getCurrentProvider(): DeliveryProvider
    {
        $user = auth()->user();

        // Find the provider associated with the current user
        $provider = DeliveryProvider::where('tenant_id', tenant()?->id)
            ->whereHas('teamMembers', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->first();

        if (! $provider) {
            throw new \Exception('Provider not found for current user');
        }

        return $provider;
    }

    /**
     * Transform team member for API response.
     */
    private function transformTeamMember(User $user, ?ProviderTeamMember $teamMember = null): array
    {
        return [
            'id' => $user->id,
            'team_member_id' => $teamMember?->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'name' => $user->first_name.' '.$user->last_name,
            'email' => $user->email,
            'phone' => $user->phone_number,
            'role' => $teamMember?->role->value ?? 'driver',
            'status' => $user->is_active ? 'active' : 'inactive',
            'is_active' => $user->is_active,
            'availability_status' => $teamMember?->availability_status->value ?? 'offline',
            'joined_at' => $teamMember?->created_at->toISOString() ?? $user->created_at->toISOString(),
            'created_at' => $teamMember?->created_at->toISOString() ?? $user->created_at->toISOString(),
        ];
    }

    /**
     * Generate a secure temporary password.
     */
    private function generateTemporaryPassword(): string
    {
        // Generate a secure 12-character password with mixed case, numbers, and symbols
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*';

        $password = '';
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)]; // At least 1 uppercase
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)]; // At least 1 lowercase
        $password .= $numbers[random_int(0, strlen($numbers) - 1)]; // At least 1 number
        $password .= $symbols[random_int(0, strlen($symbols) - 1)]; // At least 1 symbol

        // Fill the rest with random characters
        $allChars = $uppercase . $lowercase . $numbers . $symbols;
        for ($i = 4; $i < 12; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        // Shuffle the password to randomize character positions
        return str_shuffle($password);
    }
}
