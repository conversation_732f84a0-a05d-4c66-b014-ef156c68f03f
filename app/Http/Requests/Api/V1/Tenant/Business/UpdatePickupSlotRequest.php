<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1\Tenant\Business;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePickupSlotRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'day_of_week' => [
                'sometimes',
                'integer',
                'between:0,6',
            ],
            'start_time' => [
                'sometimes',
                'date_format:H:i',
            ],
            'end_time' => [
                'sometimes',
                'date_format:H:i',
                'after:start_time',
            ],
            'max_orders' => [
                'sometimes',
                'nullable',
                'integer',
                'min:1',
                'max:1000',
            ],
            'is_active' => [
                'sometimes',
                'boolean',
            ],
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'day_of_week.between' => 'Day of week must be between 0 (Sunday) and 6 (Saturday).',
            'start_time.date_format' => 'Start time must be in HH:MM format.',
            'end_time.date_format' => 'End time must be in HH:MM format.',
            'end_time.after' => 'End time must be after start time.',
            'max_orders.integer' => 'Maximum orders must be a number.',
            'max_orders.min' => 'Maximum orders must be at least 1.',
            'max_orders.max' => 'Maximum orders cannot exceed 1000.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'day_of_week' => 'day of week',
            'start_time' => 'start time',
            'end_time' => 'end time',
            'max_orders' => 'maximum orders',
            'is_active' => 'active status',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check for overlapping time slots on the same day and branch (excluding current slot)
            if (! $validator->errors()->has('start_time') && ! $validator->errors()->has('end_time')) {
                $this->validateNoOverlappingSlots($validator);
            }

            // Validate time slot duration (minimum 30 minutes)
            if (! $validator->errors()->has('start_time') && ! $validator->errors()->has('end_time')) {
                $this->validateMinimumDuration($validator);
            }

            // Validate that reducing max_orders doesn't conflict with existing orders
            if ($this->has('max_orders') && ! $validator->errors()->has('max_orders')) {
                $this->validateMaxOrdersReduction($validator);
            }
        });
    }

    /**
     * Validate that there are no overlapping time slots.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     */
    private function validateNoOverlappingSlots($validator): void
    {
        $pickupSlot = $this->route('pickupSlot');

        // Only validate if time-related fields are being updated
        if (! $this->has('start_time') && ! $this->has('end_time') && ! $this->has('day_of_week')) {
            return;
        }

        $startTime = $this->input('start_time', $pickupSlot->start_time->format('H:i'));
        $endTime = $this->input('end_time', $pickupSlot->end_time->format('H:i'));
        $dayOfWeek = $this->input('day_of_week', $pickupSlot->day_of_week);

        $overlapping = \App\Models\PickupSlot::where('business_branch_id', $pickupSlot->business_branch_id)
            ->where('day_of_week', $dayOfWeek)
            ->where('is_active', true)
            ->where('id', '!=', $pickupSlot->id) // Exclude current slot
            ->where(function ($query) use ($startTime, $endTime) {
                $query->where(function ($q) use ($startTime) {
                    // New slot starts during existing slot
                    $q->where('start_time', '<=', $startTime)
                        ->where('end_time', '>', $startTime);
                })->orWhere(function ($q) use ($endTime) {
                    // New slot ends during existing slot
                    $q->where('start_time', '<', $endTime)
                        ->where('end_time', '>=', $endTime);
                })->orWhere(function ($q) use ($startTime, $endTime) {
                    // New slot completely contains existing slot
                    $q->where('start_time', '>=', $startTime)
                        ->where('end_time', '<=', $endTime);
                });
            })
            ->exists();

        if ($overlapping) {
            $validator->errors()->add(
                'start_time',
                'This time slot overlaps with an existing pickup slot for the same day and branch.'
            );
        }
    }

    /**
     * Validate minimum duration of 30 minutes.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     */
    private function validateMinimumDuration($validator): void
    {
        $pickupSlot = $this->route('pickupSlot');

        $startTime = \Carbon\Carbon::createFromFormat('H:i',
            $this->input('start_time', $pickupSlot->start_time->format('H:i'))
        );
        $endTime = \Carbon\Carbon::createFromFormat('H:i',
            $this->input('end_time', $pickupSlot->end_time->format('H:i'))
        );

        $durationMinutes = $endTime->diffInMinutes($startTime);

        if ($durationMinutes < 30) {
            $validator->errors()->add(
                'end_time',
                'Pickup slot must be at least 30 minutes long.'
            );
        }

        if ($durationMinutes > 720) { // 12 hours
            $validator->errors()->add(
                'end_time',
                'Pickup slot cannot be longer than 12 hours.'
            );
        }
    }

    /**
     * Validate that reducing max_orders doesn't conflict with existing orders.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     */
    private function validateMaxOrdersReduction($validator): void
    {
        $pickupSlot = $this->route('pickupSlot');
        $newMaxOrders = $this->input('max_orders');

        // If setting to null (unlimited), no validation needed
        if (is_null($newMaxOrders)) {
            return;
        }

        // If current max_orders is null (unlimited), any number is fine
        if (is_null($pickupSlot->max_orders)) {
            return;
        }

        // If increasing or keeping the same, no validation needed
        if ($newMaxOrders >= $pickupSlot->max_orders) {
            return;
        }

        // Check if there are future orders that would exceed the new limit
        $futureOrdersCount = $pickupSlot->orders()
            ->whereDate('scheduled_pickup_time', '>=', today())
            ->count();

        if ($futureOrdersCount > $newMaxOrders) {
            $validator->errors()->add(
                'max_orders',
                "Cannot reduce maximum orders to {$newMaxOrders}. There are {$futureOrdersCount} future orders scheduled for this slot."
            );
        }
    }
}
