<?php

declare(strict_types=1);

namespace App\Listeners\Business;

use App\Events\Business\StaffAdded;
use App\Events\Business\TeamMemberStatusChanged;
use App\Events\Delivery\ProviderStaffAdded;
use App\Events\Delivery\ProviderTeamMemberStatusChanged;
use App\Events\Delivery\ServiceAreaUpdated;
use App\Events\Delivery\VehicleAdded;
use App\Events\System\ApiKeyGenerated;
use App\Events\User\FeedbackSubmitted;
use App\Notifications\Business\StaffAddedNotification;
use App\Notifications\Delivery\ProviderStaffAddedNotification;
use App\Notifications\Delivery\ServiceAreaUpdatedNotification;
use App\Notifications\System\SystemAnnouncement;
use App\Notifications\Delivery\VehicleAddedNotification;
use App\Notifications\System\ApiKeyGeneratedNotification;
use App\Notifications\User\FeedbackSubmittedNotification;
use App\Services\System\LoggingService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

/**
 * Send Business Management Notification Listener
 *
 * Handles business management events and sends appropriate notifications
 */
class SendBusinessManagementNotification implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        private LoggingService $loggingService
    ) {}

    /**
     * Handle feedback submitted events
     */
    public function handleFeedbackSubmitted(FeedbackSubmitted $event): void
    {
        try {
            // Notify business about feedback
            if ($event->businessTenantId && $event->order->business) {
                $businessOwner = $event->order->business->owner;
                if ($businessOwner) {
                    $businessOwner->notify(new FeedbackSubmittedNotification(
                        $event->order,
                        $event->customer,
                        $event->rating,
                        $event->comment,
                        $event->feedbackType
                    ));
                }
            }

            // Notify delivery provider about feedback if applicable
            if ($event->deliveryProviderId && $event->feedbackType === 'delivery') {
                // Implementation for delivery provider notification
                // This would require getting the delivery provider from the order
            }

            $this->loggingService->logInfo('Feedback submitted notification sent', [
                'order_id' => $event->order->id,
                'customer_id' => $event->customer->id,
                'rating' => $event->rating,
                'feedback_type' => $event->feedbackType,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send feedback submitted notification', $e, [
                'order_id' => $event->order->id,
                'customer_id' => $event->customer->id,
            ]);
            throw $e;
        }
    }

    /**
     * Handle API key generated events
     */
    public function handleApiKeyGenerated(ApiKeyGenerated $event): void
    {
        try {
            $event->user->notify(new ApiKeyGeneratedNotification(
                $event->user,
                $event->keyName,
                $event->keyId,
                $event->abilities,
                $event->keyType
            ));

            $this->loggingService->logInfo('API key generated notification sent', [
                'user_id' => $event->user->id,
                'key_name' => $event->keyName,
                'key_type' => $event->keyType,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send API key generated notification', $e, [
                'user_id' => $event->user->id,
                'key_name' => $event->keyName,
            ]);
            throw $e;
        }
    }

    /**
     * Handle service area updated events
     */
    public function handleServiceAreaUpdated(ServiceAreaUpdated $event): void
    {
        try {
            // Notify the provider owner
            $providerOwner = $event->provider->owner;
            if ($providerOwner) {
                $providerOwner->notify(new ServiceAreaUpdatedNotification(
                    $event->provider,
                    $event->updatedBy,
                    $event->updateType,
                    $event->affectedZones
                ));
            }

            // Notify other staff members if different from updater
            $staffMembers = $event->provider->staff()->where('id', '!=', $event->updatedBy->id)->get();
            foreach ($staffMembers as $staff) {
                $staff->notify(new ServiceAreaUpdatedNotification(
                    $event->provider,
                    $event->updatedBy,
                    $event->updateType,
                    $event->affectedZones
                ));
            }

            $this->loggingService->logInfo('Service area updated notification sent', [
                'provider_id' => $event->provider->id,
                'updated_by_id' => $event->updatedBy->id,
                'update_type' => $event->updateType,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send service area updated notification', $e, [
                'provider_id' => $event->provider->id,
                'updated_by_id' => $event->updatedBy->id,
            ]);
            throw $e;
        }
    }

    /**
     * Handle staff added events
     */
    public function handleStaffAdded(StaffAdded $event): void
    {
        try {
            // Notify the new staff member
            $event->staffMember->notify(new StaffAddedNotification(
                $event->staffMember,
                $event->business,
                $event->addedBy,
                $event->role,
                $event->temporaryPassword
            ));

            $this->loggingService->logInfo('Staff added notification sent', [
                'staff_member_id' => $event->staffMember->id,
                'business_id' => $event->business->id,
                'added_by_id' => $event->addedBy->id,
                'role' => $event->role,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send staff added notification', $e, [
                'staff_member_id' => $event->staffMember->id,
                'business_id' => $event->business->id,
            ]);
            throw $e;
        }
    }

    /**
     * Handle vehicle added events
     */
    public function handleVehicleAdded(VehicleAdded $event): void
    {
        try {
            // Notify the provider owner
            $providerOwner = $event->provider->owner;
            if ($providerOwner) {
                $providerOwner->notify(new VehicleAddedNotification(
                    $event->vehicle,
                    $event->provider,
                    $event->addedBy,
                    $event->assignedDriver
                ));
            }

            // Notify assigned driver if different from owner
            if ($event->assignedDriver && $event->assignedDriver->id !== $providerOwner?->id) {
                $event->assignedDriver->notify(new VehicleAddedNotification(
                    $event->vehicle,
                    $event->provider,
                    $event->addedBy,
                    $event->assignedDriver
                ));
            }

            $this->loggingService->logInfo('Vehicle added notification sent', [
                'vehicle_id' => $event->vehicle->id,
                'provider_id' => $event->provider->id,
                'added_by_id' => $event->addedBy->id,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send vehicle added notification', $e, [
                'vehicle_id' => $event->vehicle->id,
                'provider_id' => $event->provider->id,
            ]);
            throw $e;
        }
    }

    /**
     * Handle provider staff added events
     */
    public function handleProviderStaffAdded(ProviderStaffAdded $event): void
    {
        try {
            // Notify the new staff member
            $event->staffMember->notify(new ProviderStaffAddedNotification(
                $event->staffMember,
                $event->provider,
                $event->addedBy,
                $event->role,
                $event->temporaryPassword
            ));

            $this->loggingService->logInfo('Provider staff added notification sent', [
                'staff_member_id' => $event->staffMember->id,
                'provider_id' => $event->provider->id,
                'added_by_id' => $event->addedBy->id,
                'role' => $event->role,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send provider staff added notification', $e, [
                'staff_member_id' => $event->staffMember->id,
                'provider_id' => $event->provider->id,
            ]);
            throw $e;
        }
    }

    /**
     * Handle team member status changed events
     */
    public function handleTeamMemberStatusChanged(TeamMemberStatusChanged $event): void
    {
        try {
            $title = match($event->action) {
                'activated' => 'Account Activated',
                'deactivated' => 'Account Deactivated',
                'role_assigned' => 'Admin Role Assigned',
                'ownership_transferred' => 'Business Ownership Transferred',
                default => 'Account Status Changed'
            };

            $type = match($event->action) {
                'activated' => 'success',
                'deactivated' => 'warning',
                'role_assigned' => 'info',
                'ownership_transferred' => 'success',
                default => 'info'
            };

            $event->teamMember->notify(new SystemAnnouncement(
                $title,
                $event->message ?? 'Your account status has been updated.',
                $type
            ));

            $this->loggingService->logInfo('Team member status change notification sent', [
                'team_member_id' => $event->teamMember->id,
                'business_id' => $event->business->id,
                'action' => $event->action,
                'changed_by_id' => $event->changedBy->id,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send team member status change notification', $e, [
                'team_member_id' => $event->teamMember->id,
                'business_id' => $event->business->id,
                'action' => $event->action,
            ]);
            throw $e;
        }
    }

    /**
     * Handle provider team member status changed events
     */
    public function handleProviderTeamMemberStatusChanged(ProviderTeamMemberStatusChanged $event): void
    {
        try {
            $title = match($event->action) {
                'removed' => 'Team Membership Ended',
                'activated' => 'Account Activated',
                'deactivated' => 'Account Deactivated',
                'role_changed' => 'Role Updated',
                default => 'Account Status Changed'
            };

            $type = match($event->action) {
                'removed' => 'warning',
                'activated' => 'success',
                'deactivated' => 'warning',
                'role_changed' => 'info',
                default => 'info'
            };

            $event->teamMember->notify(new SystemAnnouncement(
                $title,
                $event->message ?? 'Your account status has been updated.',
                $type
            ));

            $this->loggingService->logInfo('Provider team member status change notification sent', [
                'team_member_id' => $event->teamMember->id,
                'provider_id' => $event->provider->id,
                'action' => $event->action,
                'changed_by_id' => $event->changedBy->id,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send provider team member status change notification', $e, [
                'team_member_id' => $event->teamMember->id,
                'provider_id' => $event->provider->id,
                'action' => $event->action,
            ]);
            throw $e;
        }
    }
}
