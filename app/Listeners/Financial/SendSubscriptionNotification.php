<?php

declare(strict_types=1);

namespace App\Listeners\Financial;

use App\Events\Financial\BankAccountVerified;
use App\Events\User\KycVerificationCompleted;
use App\Events\User\UserSubscriptionStatusChanged;

use App\Notifications\Financial\SubscriptionCreatedNotification;
use App\Notifications\Financial\SubscriptionExpiredNotification;
use App\Notifications\Financial\SubscriptionRenewedNotification;
use App\Services\System\LoggingService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

/**
 * Send Subscription Notification Listener
 *
 * Handles all subscription-related events and sends appropriate notifications
 */
class SendSubscriptionNotification implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Handle subscription status changed event (consolidated)
     */
    public function handleSubscriptionStatusChanged(UserSubscriptionStatusChanged $event): void
    {
        try {
            $notification = match($event->changeType) {
                'created', 'upgraded' => new SubscriptionCreatedNotification(
                    $event->subscription,
                    $event->changeType,
                    $event->isUpgrade(),
                    $event->previousSubscription,
                    $event->metadata
                ),
                'renewed' => new SubscriptionRenewedNotification(
                    $event->subscription,
                    'auto', // renewal type
                    $event->amountPaid ?? 0.0,
                    'NGN', // currency
                    null, // previous expiry date
                    $event->paymentReference,
                    $event->metadata
                ),
                'expired', 'cancelled' => new SubscriptionExpiredNotification(
                    $event->subscription,
                    $event->reason,
                    $event->gracePeriodEnds !== null,
                    $event->gracePeriodEnds,
                    $event->failedPaymentAttempts,
                    $event->metadata
                ),
                default => null
            };

            if ($notification) {
                $event->user->notify($notification);
            }

            $this->loggingService->logInfo('Subscription status change notification sent', [
                'subscription_id' => $event->subscription->id,
                'user_id' => $event->user->id,
                'change_type' => $event->changeType,
                'old_status' => $event->oldStatus->value,
                'new_status' => $event->newStatus->value,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send subscription status change notification', $e, [
                'subscription_id' => $event->subscription->id,
                'user_id' => $event->user->id,
                'change_type' => $event->changeType,
            ]);

            throw $e;
        }
    }



    /**
     * Handle KYC verification completed event
     */
    public function handleKycVerificationCompleted(KycVerificationCompleted $event): void
    {
        try {
            // For now, log the event - specific KYC notification can be added later
            $this->loggingService->logInfo('KYC verification completed', [
                'user_id' => $event->user->id,
                'verification_type' => $event->verificationType,
                'verification_status' => $event->verificationStatus,
                'verification_level' => $event->verificationLevel,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to handle KYC verification completed event', $e, [
                'user_id' => $event->user->id,
            ]);

            throw $e;
        }
    }

    /**
     * Handle bank account verified event
     */
    public function handleBankAccountVerified(BankAccountVerified $event): void
    {
        try {
            // For now, log the event - specific bank verification notification can be added later
            $this->loggingService->logInfo('Bank account verified', [
                'user_id' => $event->user->id,
                'verification_id' => $event->verification->id,
                'verification_method' => $event->verificationMethod,
                'payout_eligible' => $event->isPayoutEligible,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to handle bank account verified event', $e, [
                'user_id' => $event->user->id,
            ]);

            throw $e;
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['notifications', 'subscriptions', 'billing', 'kyc', 'verification'];
    }
}
