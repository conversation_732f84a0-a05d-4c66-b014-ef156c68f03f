<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Enums\User\AuthProvider;
use App\Models\Business\Business;
use App\Models\Delivery\Delivery;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Delivery\Order;
use App\Models\Delivery\Rating;
use App\Models\Delivery\UserDelivery;
use App\Models\Financial\Payment;
use App\Models\Financial\PaymentMethod;
use App\Models\Financial\Referral;
use App\Models\Financial\UserReward;
use App\Models\Financial\UserSubscription;
use App\Models\System\AuditLog;
use App\Models\System\Tenant;
use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Silber\Bouncer\Database\HasRolesAndAbilities;

/**
 * User Model
 *
 * @property string $id
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property string|null $phone_number
 * @property string $password
 * @property string|null $timezone User's timezone for displaying timestamps
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property \Illuminate\Support\Carbon|null $phone_verified_at
 * @property string|null $remember_token
 * @property bool $is_active
 * @property AuthProvider|null $auth_provider
 * @property string|null $auth_provider_id
 * @property \Illuminate\Support\Carbon|null $last_login_at
 * @property string|null $tenant_id
 * @property string $onboarding_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property array<array-key, mixed>|null $fcm_tokens FCM tokens for push notifications
 * @property array<array-key, mixed>|null $notification_preferences User notification preferences by channel and type
 * @property string|null $account_type customer, business, delivery_provider
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Silber\Bouncer\Database\Ability> $abilities
 * @property-read int|null $abilities_count
 * @property-read UserSubscription|null $activeSubscription
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Address> $addresses
 * @property-read int|null $addresses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Delivery> $assignedDeliveries
 * @property-read int|null $assigned_deliveries_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, AuditLog> $auditLogs
 * @property-read int|null $audit_logs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Business> $businesses
 * @property-read int|null $businesses_count
 * @property-read CustomerProfile|null $customerProfile
 * @property-read Address|null $defaultAddress
 * @property-read \Illuminate\Database\Eloquent\Collection<int, DeliveryProvider> $deliveryProviders
 * @property-read int|null $delivery_providers_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, EntityVerification> $entityVerifications
 * @property-read int|null $entity_verifications_count
 * @property-read string $display_name
 * @property-read string $initials
 * @property-read string $name
 * @property-read \Illuminate\Database\Eloquent\Collection<int, NotificationPreference> $notificationPreferences
 * @property-read int|null $notification_preferences_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Order> $orders
 * @property-read int|null $orders_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, PaymentMethod> $paymentMethods
 * @property-read int|null $payment_methods_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Payment> $payments
 * @property-read int|null $payments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Rating> $ratingsGiven
 * @property-read int|null $ratings_given_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Rating> $ratingsReceived
 * @property-read int|null $ratings_received_count
 * @property-read Referral|null $referralReceived
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Referral> $referralsMade
 * @property-read int|null $referrals_made_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserReward> $rewards
 * @property-read int|null $rewards_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Silber\Bouncer\Database\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserSubscription> $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, SupportTicket> $supportTickets
 * @property-read int|null $support_tickets_count
 * @property-read Tenant|null $tenant
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserDelivery> $userDeliveries
 * @property-read int|null $user_deliveries_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Notification> $userNotifications
 * @property-read int|null $user_notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserPreference> $userPreferences
 * @property-read int|null $user_preferences_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Verification> $verifications
 * @property-read int|null $verifications_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User emailVerified()
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User phoneVerified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User search(string $searchTerm)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAccountType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAuthProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAuthProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereFcmTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereIs($role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereIsAll($role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereIsNot($role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereLastLoginAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereNotificationPreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereOnboardingStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePhoneNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePhoneVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withRole(string $role)
 *
 * @mixin \Eloquent
 */
class User extends Authenticatable
{
    use Auditable, HasApiTokens, HasFactory, HasRolesAndAbilities, HasUuids, Notifiable;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\UserFactory::new();
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone_number',
        'password',
        'timezone',
        'email_verified_at',
        'phone_verified_at',
        'is_active',
        'auth_provider',
        'auth_provider_id',
        'last_login_at',
        'tenant_id',
        'onboarding_status',
        'fcm_tokens',
        'notification_preferences',
        'two_factor_secret',
        'two_factor_confirmed_at',
        'two_factor_backup_codes',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'auth_provider_id',
        'two_factor_secret',
        'two_factor_backup_codes',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'name',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'phone_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'auth_provider' => AuthProvider::class,
            'fcm_tokens' => 'array',
            'notification_preferences' => 'array',
            'two_factor_confirmed_at' => 'datetime',
        ];
    }

    /**
     * Check if the user's email is verified.
     */
    public function hasVerifiedEmail(): bool
    {
        return ! is_null($this->email_verified_at);
    }

    /**
     * Check if the user's phone is verified.
     */
    public function hasVerifiedPhone(): bool
    {
        return ! is_null($this->phone_verified_at);
    }

    /**
     * Mark the user's email as verified.
     */
    public function markEmailAsVerified(): bool
    {
        return $this->forceFill([
            'email_verified_at' => $this->freshTimestamp(),
        ])->save();
    }

    /**
     * Mark the user's phone as verified.
     */
    public function markPhoneAsVerified(): bool
    {
        return $this->forceFill([
            'phone_verified_at' => $this->freshTimestamp(),
        ])->save();
    }

    /**
     * Check if the user has two-factor authentication enabled.
     */
    public function hasTwoFactorEnabled(): bool
    {
        return !is_null($this->two_factor_secret) && !is_null($this->two_factor_confirmed_at);
    }

    /**
     * Get the tenant this user belongs to.
     *
     * @return BelongsTo<Tenant,User>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user's addresses.
     *
     * @return HasMany<Address,User>
     */
    public function addresses(): HasMany
    {
        return $this->hasMany(Address::class);
    }

    /**
     * Get the user's default address.
     *
     * @return HasOne<Address,User>
     */
    public function defaultAddress(): HasOne
    {
        return $this->hasOne(Address::class)->where('is_default', true);
    }

    /**
     * Get the user's customer profile.
     *
     * @return HasOne<CustomerProfile,User>
     */
    public function customerProfile(): HasOne
    {
        return $this->hasOne(CustomerProfile::class);
    }

    /**
     * Get the user's businesses.
     *
     * @return HasMany<Business,User>
     */
    public function businesses(): HasMany
    {
        return $this->hasMany(Business::class, 'user_id');
    }

    /**
     * Get the user's delivery providers.
     *
     * @return HasMany<DeliveryProvider,User>
     */
    public function deliveryProviders(): HasMany
    {
        return $this->hasMany(DeliveryProvider::class, 'user_id');
    }

    /**
     * Get the user's orders as a customer.
     *
     * @return HasMany<Order,User>
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'customer_id');
    }

    /**
     * Get the user's ad-hoc deliveries.
     *
     * @return HasMany<UserDelivery,User>
     */
    public function userDeliveries(): HasMany
    {
        return $this->hasMany(UserDelivery::class, 'user_id');
    }

    /**
     * Get the user's subscriptions.
     *
     * @return HasMany<UserSubscription,User>
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class, 'user_id');
    }

    /**
     * Get the user's current active subscription.
     *
     * @return HasOne<UserSubscription,User>
     */
    public function activeSubscription(): HasOne
    {
        return $this->hasOne(UserSubscription::class, 'user_id')
            ->where('status', 'active')
            ->latest();
    }

    /**
     * Get the user's payment methods.
     *
     * @return HasMany<PaymentMethod,User>
     */
    public function paymentMethods(): HasMany
    {
        return $this->hasMany(PaymentMethod::class, 'user_id');
    }

    /**
     * Get the user's payments.
     *
     * @return HasMany<Payment,User>
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'user_id');
    }

    /**
     * Get the user's ratings given.
     *
     * @return HasMany<Rating,User>
     */
    public function ratingsGiven(): HasMany
    {
        return $this->hasMany(Rating::class, 'rater_id');
    }

    /**
     * Get the user's ratings received (as a driver).
     *
     * @return MorphMany<Rating,User>
     */
    public function ratingsReceived(): MorphMany
    {
        return $this->morphMany(Rating::class, 'rateable');
    }

    /**
     * Get the user's notifications.
     *
     * @return HasMany<Notification,User>
     */
    public function userNotifications(): HasMany
    {
        return $this->hasMany(Notification::class, 'user_id');
    }

    /**
     * Get the user's notification preferences.
     *
     * @return HasMany<NotificationPreference,User>
     */
    public function notificationPreferences(): HasMany
    {
        return $this->hasMany(NotificationPreference::class, 'user_id');
    }

    /**
     * Get the user's rewards.
     *
     * @return HasMany<UserReward,User>
     */
    public function rewards(): HasMany
    {
        return $this->hasMany(UserReward::class, 'user_id');
    }

    /**
     * Get the user's referrals made.
     *
     * @return HasMany<Referral,User>
     */
    public function referralsMade(): HasMany
    {
        return $this->hasMany(Referral::class, 'referrer_user_id');
    }

    /**
     * Get the user's referral received.
     *
     * @return HasOne<Referral,User>
     */
    public function referralReceived(): HasOne
    {
        return $this->hasOne(Referral::class, 'referred_user_id');
    }

    /**
     * Get the user's support tickets.
     *
     * @return HasMany<SupportTicket,User>
     */
    public function supportTickets(): HasMany
    {
        return $this->hasMany(SupportTicket::class, 'user_id');
    }

    /**
     * Get the user's deliveries assigned as driver.
     *
     * @return HasMany<Delivery,User>
     */
    public function assignedDeliveries(): HasMany
    {
        return $this->hasMany(Delivery::class, 'assigned_driver_id');
    }

    /**
     * Get the user's entity verification records (complex verification steps).
     *
     * @return HasMany<EntityVerification,User>
     */
    public function entityVerifications(): HasMany
    {
        return $this->hasMany(EntityVerification::class, 'entity_id')
            ->where('entity_type', 'users');
    }

    /**
     * Get the user's verification records (simple verification codes).
     *
     * @return MorphMany<Verification,User>
     */
    public function verifications(): MorphMany
    {
        return $this->morphMany(Verification::class, 'verifiable');
    }

    /**
     * Get the user's audit logs.
     *
     * @return HasMany<AuditLog,User>
     */
    public function auditLogs(): HasMany
    {
        return $this->hasMany(AuditLog::class, 'user_id');
    }

    /**
     * Get the user's general preferences.
     *
     * @return HasMany<UserPreference,User>
     */
    public function userPreferences(): HasMany
    {
        return $this->hasMany(UserPreference::class, 'user_id');
    }

    /**
     * Check if user is using social login.
     */
    public function isSocialUser(): bool
    {
        return ! is_null($this->auth_provider) && $this->auth_provider !== AuthProvider::EMAIL;
    }

    /**
     * Scope a query to only include active users.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include users with verified email.
     */
    public function scopeEmailVerified($query)
    {
        return $query->whereNotNull('email_verified_at');
    }

    /**
     * Scope a query to only include users with verified phone.
     */
    public function scopePhoneVerified($query)
    {
        return $query->whereNotNull('phone_verified_at');
    }

    /**
     * Scope a query to search users by name or email.
     */
    public function scopeSearch($query, string $searchTerm)
    {
        return $query->where(function ($query) use ($searchTerm) {
            $query->where('first_name', 'like', "%{$searchTerm}%")
                ->orWhere('last_name', 'like', "%{$searchTerm}%")
                ->orWhere('email', 'like', "%{$searchTerm}%")
                ->orWhere('phone_number', 'like', "%{$searchTerm}%");
        });
    }

    /**
     * Scope a query to include users with a specific role.
     */
    public function scopeWithRole($query, string $role)
    {
        return $query->whereIs($role);
    }

    /**
     * Check if user has verified both email and phone.
     */
    public function isFullyVerified(): bool
    {
        return $this->hasVerifiedEmail() && $this->hasVerifiedPhone();
    }

    /**
     * Get the user's full name.
     */
    public function getNameAttribute(): string
    {
        return trim($this->first_name.' '.$this->last_name);
    }

    /**
     * Get the user's display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name ?: $this->email;
    }

    /**
     * Get the user's initials.
     */
    public function getInitialsAttribute(): string
    {
        $initials = '';

        if ($this->first_name) {
            $initials .= strtoupper(substr($this->first_name, 0, 1));
        }

        if ($this->last_name) {
            $initials .= strtoupper(substr($this->last_name, 0, 1));
        }

        return $initials ?: strtoupper(substr($this->email, 0, 2));
    }

    /**
     * Check if user is a customer.
     */
    public function isCustomer(): bool
    {
        return $this->isA('customer');
    }

    /**
     * Check if user is a business owner.
     */
    public function isBusinessOwner(): bool
    {
        return $this->isA('business_owner');
    }

    /**
     * Check if user is a driver.
     */
    public function isDriver(): bool
    {
        return $this->isA('driver');
    }

    /**
     * Check if user is a delivery provider.
     */
    public function isDeliveryProvider(): bool
    {
        return $this->isA('delivery_provider') || $this->isDriver();
    }

    /**
     * Check if user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->isA('admin') || $this->isA('super-admin');
    }

    /**
     * Check if user is a platform admin (can access all tenants).
     */
    public function isPlatformAdmin(): bool
    {
        return is_null($this->tenant_id) && $this->isAdmin();
    }

    /**
     * Check if user is a tenant user (belongs to specific tenant).
     */
    public function isTenantUser(): bool
    {
        return ! is_null($this->tenant_id);
    }

    /**
     * Get user type based on tenant relationship and roles.
     */
    public function getUserType(): string
    {
        if (is_null($this->tenant_id)) {
            return $this->isAdmin() ? 'platform_admin' : 'customer';
        }

        return 'tenant_user';
    }

    /**
     * Get user's total order count.
     */
    public function getTotalOrdersCount(): int
    {
        return $this->orders()->count();
    }

    /**
     * Get user's total spent amount.
     */
    public function getTotalSpentAmount(): float
    {
        return $this->orders()
            ->where('status', 'delivered')
            ->sum('total_amount');
    }

    /**
     * Get user's available reward balance.
     */
    public function getAvailableRewardBalance(): float
    {
        return $this->rewards()
            ->where('status', 'active')
            ->sum('amount_earned');
    }

    /**
     * Check if user has an active subscription.
     */
    public function hasActiveSubscription(): bool
    {
        return $this->activeSubscription()->exists();
    }

    /**
     * Update user's last login timestamp.
     */
    public function updateLastLogin(): bool
    {
        $this->update(['last_login_at' => now()]);

        return true;
    }

    /**
     * Route notifications for the FCM channel.
     */
    public function routeNotificationForFcm(): array
    {
        return $this->fcm_tokens ?? [];
    }

    /**
     * Route notifications for the Twilio channel.
     */
    public function routeNotificationForTwilio(): ?string
    {
        return $this->phone_number;
    }

    /**
     * Add FCM token for push notifications.
     */
    public function addFcmToken(string $token, string $platform = 'web'): void
    {
        $tokens = $this->fcm_tokens ?? [];

        // Remove existing token if it exists
        $tokens = array_filter($tokens, fn ($t) => $t['token'] !== $token);

        // Add new token
        $tokens[] = [
            'token' => $token,
            'platform' => $platform,
            'added_at' => now()->toISOString(),
        ];

        $this->update(['fcm_tokens' => $tokens]);
    }

    /**
     * Remove FCM token.
     */
    public function removeFcmToken(string $token): void
    {
        $tokens = $this->fcm_tokens ?? [];
        $tokens = array_filter($tokens, fn ($t) => $t['token'] !== $token);

        $this->update(['fcm_tokens' => array_values($tokens)]);
    }

    /**
     * Get notification preference for a specific channel and type.
     */
    public function getNotificationPreference(string $channel, string $type, bool $default = true): bool
    {
        $preferences = $this->notification_preferences ?? [];

        return $preferences[$channel][$type] ?? $default;
    }

    /**
     * Set notification preference for a specific channel and type.
     */
    public function setNotificationPreference(string $channel, string $type, bool $enabled): void
    {
        $preferences = $this->notification_preferences ?? [];
        $preferences[$channel][$type] = $enabled;

        $this->update(['notification_preferences' => $preferences]);
    }

    /**
     * Get default notification preferences for new users.
     */
    public static function getDefaultNotificationPreferences(): array
    {
        return [
            'push' => [
                'order_status' => true,
                'delivery_update' => true,
                'payment_confirmation' => true,
                'promotional' => false,
                'system_announcement' => true,
            ],
            'sms' => [
                'order_status' => false,
                'delivery_update' => false,
                'payment_confirmation' => true,
                'promotional' => false,
                'system_announcement' => false,
            ],
            'email' => [
                'order_status' => false,
                'delivery_update' => false,
                'payment_confirmation' => true,
                'promotional' => true,
                'system_announcement' => false,
            ],
        ];
    }
}
