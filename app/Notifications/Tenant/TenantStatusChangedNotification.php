<?php

declare(strict_types=1);

namespace App\Notifications\Tenant;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

/**
 * Unified Tenant Status Changed Notification
 *
 * Handles status change notifications for both businesses and delivery providers.
 * Replaces separate BusinessStatusChangedNotification and DeliveryProviderStatusChangedNotification.
 */
class TenantStatusChangedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly Business|DeliveryProvider $tenant,
        private readonly string $oldStatus,
        private readonly string $newStatus,
        private readonly ?string $reason = null,
        private readonly ?User $changedBy = null
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $tenantName = $this->getTenantName();
        $entityLabel = $this->getEntityLabel();
        $title = $this->getStatusChangeTitle();
        $message = $this->getStatusMessage();

        $mailMessage = (new MailMessage)
            ->subject("{$title} - {$tenantName}")
            ->greeting("Hello!")
            ->line($message);

        // Add reason if provided
        if ($this->reason) {
            $mailMessage->line("**Reason:** {$this->reason}");
        }

        // Add status-specific content
        if ($this->newStatus === 'active' || $this->newStatus === 'verified') {
            $loginUrl = $this->getLoginUrl();
            $mailMessage->action("Access {$entityLabel} Portal", $loginUrl);
            $mailMessage->line("You can now start using all available features.");
        } elseif ($this->newStatus === 'suspended' || $this->newStatus === 'inactive') {
            $mailMessage->line("Please contact support if you believe this is an error.");
        } elseif ($this->newStatus === 'pending') {
            $mailMessage->line("We will review your application and get back to you soon.");
        }

        if ($this->changedBy) {
            $mailMessage->line("This change was made by {$this->changedBy->full_name}.");
        }

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        $tenantType = $this->tenant instanceof Business ? 'business' : 'provider';
        $tenantName = $this->getTenantName();

        return [
            'title' => $this->getStatusChangeTitle(),
            'message' => $this->getStatusMessage(),
            'type' => 'tenant_status_changed',
            'tenant_type' => $tenantType,
            'tenant' => [
                'id' => $this->tenant->id,
                'name' => $tenantName,
                'type' => $tenantType,
            ],
            'status_change' => [
                'old_status' => $this->oldStatus,
                'new_status' => $this->newStatus,
                'reason' => $this->reason,
            ],
            'changed_by' => $this->changedBy ? [
                'id' => $this->changedBy->id,
                'name' => $this->changedBy->full_name,
                'email' => $this->changedBy->email,
            ] : null,
            'notification_type' => $this->getNotificationType(),
            'action_url' => in_array($this->newStatus, ['active', 'verified']) ? $this->getLoginUrl() : null,
            'action_text' => in_array($this->newStatus, ['active', 'verified']) ? "Access {$this->getEntityLabel()} Portal" : null,
        ];
    }

    /**
     * Get status change title for notifications
     */
    private function getStatusChangeTitle(): string
    {
        $entityLabel = $this->getEntityLabel();
        
        return match($this->newStatus) {
            'active', 'verified' => "{$entityLabel} Activated",
            'inactive', 'suspended' => "{$entityLabel} Suspended",
            'pending' => "{$entityLabel} Under Review",
            'rejected' => "{$entityLabel} Application Rejected",
            default => "{$entityLabel} Status Changed"
        };
    }

    /**
     * Get notification type for styling
     */
    private function getNotificationType(): string
    {
        return match($this->newStatus) {
            'active', 'verified' => 'success',
            'inactive', 'suspended', 'rejected' => 'warning',
            'pending' => 'info',
            default => 'info'
        };
    }

    /**
     * Get status message
     */
    private function getStatusMessage(): string
    {
        $tenantName = $this->getTenantName();
        $entityLabel = $this->getEntityLabel();

        return match($this->newStatus) {
            'active', 'verified' => "Your {$entityLabel} '{$tenantName}' has been activated and is now live on the platform.",
            'inactive', 'suspended' => "Your {$entityLabel} '{$tenantName}' has been suspended and is temporarily unavailable.",
            'pending' => "Your {$entityLabel} '{$tenantName}' is currently under review by our team.",
            'rejected' => "Your {$entityLabel} application '{$tenantName}' has been rejected.",
            default => "The status of your {$entityLabel} '{$tenantName}' has been changed from {$this->oldStatus} to {$this->newStatus}."
        };
    }

    /**
     * Get tenant name
     */
    private function getTenantName(): string
    {
        return $this->tenant->name ?? $this->tenant->company_name ?? $this->tenant->business_name ?? 'Unknown';
    }

    /**
     * Get login URL based on tenant type
     */
    private function getLoginUrl(): string
    {
        $tenantType = $this->tenant instanceof Business ? 'business' : 'provider';
        
        return match($tenantType) {
            'business' => config('app.tenant_url', 'https://business.deliverynexus.com') . '/login',
            'provider' => config('app.tenant_url', 'https://provider.deliverynexus.com') . '/login',
            default => config('app.url') . '/login'
        };
    }

    /**
     * Get entity label based on tenant type
     */
    private function getEntityLabel(): string
    {
        return $this->tenant instanceof Business ? 'Business' : 'Provider';
    }
}
