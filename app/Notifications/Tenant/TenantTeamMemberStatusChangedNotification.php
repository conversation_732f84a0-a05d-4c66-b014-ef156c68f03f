<?php

declare(strict_types=1);

namespace App\Notifications\Tenant;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

/**
 * Unified Tenant Team Member Status Changed Notification
 *
 * Handles team member status change notifications for both businesses and delivery providers.
 * Replaces separate team member status notifications.
 */
class TenantTeamMemberStatusChangedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly User $teamMember,
        private readonly Business|DeliveryProvider $tenant,
        private readonly User $changedBy,
        private readonly string $action,
        private readonly ?string $newRole = null,
        private readonly ?string $message = null
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $tenantName = $this->getTenantName();
        $entityLabel = $this->getEntityLabel();
        $title = $this->getActionTitle();
        $message = $this->message ?? $this->getDefaultMessage();

        $mailMessage = (new MailMessage)
            ->subject("{$title} - {$tenantName}")
            ->greeting("Hello {$this->teamMember->first_name}!")
            ->line($message);

        // Add role-specific information
        if ($this->newRole && $this->action === 'role_assigned') {
            $mailMessage->line("**New Role:** {$this->newRole}");
        }

        // Add action-specific content
        if ($this->action === 'ownership_transferred') {
            $mailMessage->line("You now have full administrative access to {$tenantName}.");
        } elseif ($this->action === 'activated') {
            $loginUrl = $this->getLoginUrl();
            $mailMessage->action("Access {$entityLabel} Portal", $loginUrl);
        }

        $mailMessage->line("If you have any questions, please contact {$this->changedBy->full_name}.");

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        $tenantType = $this->tenant instanceof Business ? 'business' : 'provider';
        $tenantName = $this->getTenantName();

        return [
            'title' => $this->getActionTitle(),
            'message' => $this->message ?? $this->getDefaultMessage(),
            'type' => 'tenant_team_member_status_changed',
            'tenant_type' => $tenantType,
            'tenant' => [
                'id' => $this->tenant->id,
                'name' => $tenantName,
                'type' => $tenantType,
            ],
            'changed_by' => [
                'id' => $this->changedBy->id,
                'name' => $this->changedBy->full_name,
                'email' => $this->changedBy->email,
            ],
            'action' => $this->action,
            'new_role' => $this->newRole,
            'notification_type' => $this->getNotificationType(),
            'action_url' => $this->action === 'activated' ? $this->getLoginUrl() : null,
            'action_text' => $this->action === 'activated' ? "Access {$this->getEntityLabel()} Portal" : null,
        ];
    }

    /**
     * Get action title for notifications
     */
    private function getActionTitle(): string
    {
        return match($this->action) {
            'activated' => 'Account Activated',
            'deactivated' => 'Account Deactivated',
            'role_assigned' => 'Role Updated',
            'ownership_transferred' => 'Ownership Transferred',
            'removed' => 'Team Membership Ended',
            default => 'Account Status Changed'
        };
    }

    /**
     * Get notification type for styling
     */
    private function getNotificationType(): string
    {
        return match($this->action) {
            'activated', 'ownership_transferred' => 'success',
            'deactivated', 'removed' => 'warning',
            'role_assigned' => 'info',
            default => 'info'
        };
    }

    /**
     * Get default message based on action
     */
    private function getDefaultMessage(): string
    {
        $tenantName = $this->getTenantName();
        $entityLabel = $this->getEntityLabel();

        return match($this->action) {
            'activated' => "Your account has been activated. You can now access the {$entityLabel} portal.",
            'deactivated' => "Your access to {$tenantName} has been temporarily suspended.",
            'role_assigned' => "Your role has been updated to {$this->newRole} for {$tenantName}.",
            'ownership_transferred' => "You are now the owner of {$tenantName}.",
            'removed' => "Your membership with {$tenantName} has been ended.",
            default => "Your account status has been updated."
        };
    }

    /**
     * Get tenant name
     */
    private function getTenantName(): string
    {
        return $this->tenant->name ?? $this->tenant->company_name ?? $this->tenant->business_name ?? 'Unknown';
    }

    /**
     * Get login URL based on tenant type
     */
    private function getLoginUrl(): string
    {
        $tenantType = $this->tenant instanceof Business ? 'business' : 'provider';
        
        return match($tenantType) {
            'business' => config('app.tenant_url', 'https://business.deliverynexus.com') . '/login',
            'provider' => config('app.tenant_url', 'https://provider.deliverynexus.com') . '/login',
            default => config('app.url') . '/login'
        };
    }

    /**
     * Get entity label based on tenant type
     */
    private function getEntityLabel(): string
    {
        return $this->tenant instanceof Business ? 'Business' : 'Provider';
    }
}
