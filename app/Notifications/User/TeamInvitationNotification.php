<?php

declare(strict_types=1);

namespace App\Notifications\User;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\TeamInvitation;
use App\Models\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use NotificationChannels\Twilio\TwilioSmsMessage;

/**
 * Team Invitation Notification
 *
 * Sent when a user is invited to join a business or delivery provider team.
 * Uses token-based invitation system with email and SMS channels.
 */
class TeamInvitationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly TeamInvitation $invitation,
        private readonly Business|DeliveryProvider $entity,
        private readonly User $invitedBy
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'twilio'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $entityName = $this->entity instanceof Business 
            ? $this->entity->business_name 
            : $this->entity->company_name;

        $entityType = $this->entity instanceof Business ? 'business' : 'delivery provider';
        
        $acceptUrl = url("/team/invitation/accept/{$this->invitation->token}");

        return (new MailMessage)
            ->subject("You've been invited to join {$entityName}")
            ->greeting("Hello {$this->invitation->metadata['first_name']}!")
            ->line("You have been invited by {$this->invitedBy->full_name} to join {$entityName} as a {$this->invitation->role}.")
            ->line("As a team member, you'll have access to the {$entityType} dashboard and can help manage operations.")
            ->action('Accept Invitation', $acceptUrl)
            ->line('This invitation will expire in 3 days.')
            ->line('If you did not expect this invitation, you can safely ignore this email.')
            ->salutation('Best regards, The DeliveryNexus Team');
    }

    /**
     * Get the Twilio SMS representation of the notification.
     */
    public function toTwilio(object $notifiable): TwilioSmsMessage
    {
        $entityName = $this->entity instanceof Business 
            ? $this->entity->business_name 
            : $this->entity->company_name;

        $acceptUrl = url("/team/invitation/accept/{$this->invitation->token}");

        return TwilioSmsMessage::create()
            ->content("DeliveryNexus: You've been invited to join {$entityName} as {$this->invitation->role}. Accept: {$acceptUrl}");
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'invitation_id' => $this->invitation->id,
            'email' => $this->invitation->email,
            'role' => $this->invitation->role,
            'entity_type' => $this->invitation->metadata['invitation_type'],
            'entity_id' => $this->entity->id,
            'entity_name' => $this->entity instanceof Business 
                ? $this->entity->business_name 
                : $this->entity->company_name,
            'invited_by' => [
                'id' => $this->invitedBy->id,
                'name' => $this->invitedBy->full_name,
                'email' => $this->invitedBy->email,
            ],
            'expires_at' => $this->invitation->expires_at->toISOString(),
            'accept_url' => url("/team/invitation/accept/{$this->invitation->token}"),
            'type' => 'team_invitation',
        ];
    }
}
