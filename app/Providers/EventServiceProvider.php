<?php

declare(strict_types=1);

namespace App\Providers;

use App\Events\Business\BusinessDocumentsUploaded;
use App\Events\Business\BusinessFirstOrderReceived;
use App\Events\Business\BusinessOnboardingCompleted;
use App\Events\Business\BusinessOnboardingStarted;
use App\Events\Business\BusinessPaymentSetupCompleted;
use App\Events\Business\BusinessProfileCompleted;
use App\Events\Business\BusinessReopened;
use App\Events\Business\BusinessSetupStepCompleted;
use App\Events\Business\BusinessStatusChanged;
use App\Events\Business\BusinessTemporarilyClosed;
use App\Events\Business\BusinessVerificationCompleted;
use App\Events\Business\InventoryStockIn;
use App\Events\Business\InventoryStockOut;
use App\Events\Business\ProductOutOfStock;
use App\Events\Business\PromotionApplied;
use App\Events\Business\PurchaseOrderCreated;
use App\Events\Business\StaffAdded;
use App\Events\Business\StockLevelCritical;
use App\Events\Business\TeamMemberStatusChanged;
use App\Events\Business\StockLevelLow;
use App\Events\Business\StockReplenished;
use App\Events\Delivery\AdHocDeliveryCompleted;
use App\Events\Delivery\AdHocDeliveryCreated;
use App\Events\Delivery\AdHocDeliveryProviderAssigned;
use App\Events\Delivery\CustomPolygonZoneCreated;
use App\Events\Delivery\DeliveryAssigned;
use App\Events\Delivery\DeliveryCompleted;
use App\Events\Delivery\DeliveryDeclined;
use App\Events\Delivery\DeliveryProviderStatusChanged;
use App\Events\Delivery\DeliveryRequested;
use App\Events\Delivery\DeliveryRequestStatusChanged;
use App\Events\Delivery\DriverOnlineStatusChanged;
use App\Events\Delivery\InterstateDeliveryDetected;
use App\Events\Delivery\OrderAccepted;
use App\Events\Delivery\OrderCancelled;
use App\Events\Delivery\OrderCreated;
use App\Events\Delivery\OrderRejected;
use App\Events\Delivery\OrderStatusUpdated;
use App\Events\Delivery\PricingCalculated;
use App\Events\Delivery\ProviderAvailabilityChanged;
use App\Events\Delivery\ProviderOnboardingCompleted;
use App\Events\Delivery\ProviderStaffAdded;
use App\Events\Delivery\ProviderTeamMemberStatusChanged;
use App\Events\Delivery\ServiceAreaUpdated;
use App\Events\Delivery\VehicleAdded;
use App\Events\Delivery\VehicleBreakdownReported;
use App\Events\Delivery\VehicleStatusChanged;
use App\Events\Financial\BankAccountVerified;
use App\Events\Financial\CommissionCalculated;
use App\Events\Financial\CommissionStatusChanged;
use App\Events\Financial\DisputeCreated;
use App\Events\Financial\DisputeStatusChanged;
use App\Events\Financial\PaymentFailed;
use App\Events\Financial\PaymentProcessed;
use App\Events\Financial\PaymentRefunded;
use App\Events\Financial\PaymentStatusChanged;
use App\Events\Financial\PayoutFailed;
use App\Events\Financial\PayoutProcessed;
use App\Events\Financial\PayoutStatusChanged;
use App\Events\Financial\ReferralSuccessful;
use App\Events\Financial\SubscriptionCancelled;
use App\Events\Financial\SubscriptionCreated;
use App\Events\Financial\SubscriptionDowngraded;
use App\Events\Financial\SubscriptionExpired;
use App\Events\Financial\SubscriptionRenewed;
use App\Events\Financial\SubscriptionUpgraded;
use App\Events\System\AiPredictionGenerated;
use App\Events\System\ApiKeyGenerated;
use App\Events\System\ApiRateLimitExceeded;
use App\Events\System\FraudDetected;
use App\Events\System\ReportGenerationCompleted;
use App\Events\System\ReportStatusChanged;
use App\Events\System\SuspiciousActivityDetected;
use App\Events\System\TenantStatusChanged;
use App\Events\System\WebhookDeliveryFailed;
use App\Events\System\WebhookStatusChanged;
use App\Events\User\AccountSuspended;
use App\Events\User\CustomerRegistered;
use App\Events\User\FeedbackSubmitted;
use App\Events\User\KycTierAdvanced;
use App\Events\User\KycVerificationCompleted;
use App\Events\User\SupportTicketCreated;
use App\Events\User\SupportTicketStatusChanged;
use App\Events\User\UserRegistered;
use App\Events\User\UserStatusChanged;
use App\Events\User\UserCreatedByAdmin;
use App\Events\User\UserSubscriptionStatusChanged;
use App\Events\User\VerificationApproved;
use App\Events\User\VerificationSubmitted;
use App\Listeners\Auth\LogFailedLogin;
use App\Listeners\Auth\LogLogout;
use App\Listeners\Auth\LogPasswordReset;
use App\Listeners\Auth\LogSuccessfulLogin;
use App\Listeners\Business\SendBusinessManagementNotification;
use App\Listeners\Business\SendBusinessOnboardingNotification;
use App\Listeners\Business\SendBusinessOperationNotification;
use App\Listeners\Business\SendBusinessVerificationNotification;
use App\Listeners\Business\SendCriticalBusinessNotifications;
use App\Listeners\Business\SendStockLevelAlert;
use App\Listeners\Business\SendStockLevelCriticalAlert;
use App\Listeners\Delivery\SendAdHocDeliveryNotifications;
use App\Listeners\Delivery\SendDeliveryAssignedNotification;
use App\Listeners\Delivery\SendDeliveryCompletedNotification;
use App\Listeners\Delivery\SendDriverStatusNotification;
use App\Listeners\Delivery\SendOrderCancelledNotification;
use App\Listeners\Delivery\SendOrderStatusNotification;
use App\Listeners\Delivery\SendOrderWorkflowNotification;
use App\Listeners\Financial\SendPaymentNotification;
use App\Listeners\Financial\SendPayoutNotification;
use App\Listeners\Financial\SendSubscriptionNotification;
use App\Listeners\System\SendCriticalSystemNotifications;
use App\Listeners\System\SendSecurityAlert;
use App\Listeners\User\SendAdvancedNotifications;
use App\Listeners\User\SendStatusChangeNotifications;
use App\Listeners\User\SendUserWelcomeNotification;
use Illuminate\Auth\Events\Failed;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

/**
 * Event Service Provider
 *
 * Registers event listeners for the notification system
 * Implements the refined SMS strategy for DeliveryNexus
 */
class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        // Laravel authentication events
        Login::class => [
            LogSuccessfulLogin::class,
        ],

        Logout::class => [
            LogLogout::class,
        ],

        Failed::class => [
            LogFailedLogin::class,
        ],

        PasswordReset::class => [
            LogPasswordReset::class,
        ],

        // Laravel default events
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // User Events
        UserRegistered::class => [
            SendUserWelcomeNotification::class,
        ],

        UserCreatedByAdmin::class => [
            SendUserWelcomeNotification::class.'@handleUserCreatedByAdmin',
        ],

        // Subscription & Billing Events
        SubscriptionCreated::class => [
            SendSubscriptionNotification::class.'@handleSubscriptionCreated',
        ],

        SubscriptionRenewed::class => [
            SendSubscriptionNotification::class.'@handleSubscriptionRenewed',
        ],

        SubscriptionExpired::class => [
            SendSubscriptionNotification::class.'@handleSubscriptionExpired',
        ],

        PaymentFailed::class => [
            SendSubscriptionNotification::class.'@handlePaymentFailed',
        ],

        SubscriptionUpgraded::class => [
            SendSubscriptionNotification::class.'@handleSubscriptionUpgraded',
        ],

        SubscriptionDowngraded::class => [
            SendAdvancedNotifications::class.'@handleSubscriptionDowngraded',
        ],

        SubscriptionCancelled::class => [
            SendSubscriptionNotification::class.'@handleSubscriptionCancelled',
        ],

        // KYC & Verification Events
        KycVerificationCompleted::class => [
            SendSubscriptionNotification::class.'@handleKycVerificationCompleted',
        ],

        BankAccountVerified::class => [
            SendSubscriptionNotification::class.'@handleBankAccountVerified',
        ],

        KycTierAdvanced::class => [
            SendAdvancedNotifications::class.'@handleKycTierAdvanced',
        ],

        // Customer Events
        CustomerRegistered::class => [
            SendOrderWorkflowNotification::class.'@handleCustomerRegistered',
        ],

        // Order Events (consolidated)
        OrderStatusUpdated::class => [
            SendOrderStatusNotification::class,
        ],

        // Delivery Workflow Events
        DeliveryRequested::class => [
            SendOrderWorkflowNotification::class.'@handleDeliveryRequested',
        ],

        DeliveryDeclined::class => [
            SendOrderWorkflowNotification::class.'@handleDeliveryDeclined',
        ],

        // Business Events
        BusinessVerificationCompleted::class => [
            SendBusinessVerificationNotification::class,
        ],

        BusinessOnboardingStarted::class => [
            SendBusinessOnboardingNotification::class.'@handleBusinessOnboardingStarted',
        ],

        BusinessProfileCompleted::class => [
            SendBusinessOnboardingNotification::class.'@handleBusinessProfileCompleted',
        ],

        BusinessDocumentsUploaded::class => [
            SendBusinessOnboardingNotification::class.'@handleBusinessDocumentsUploaded',
        ],

        BusinessPaymentSetupCompleted::class => [
            SendBusinessOnboardingNotification::class.'@handleBusinessPaymentSetupCompleted',
        ],

        BusinessFirstOrderReceived::class => [
            SendBusinessOnboardingNotification::class.'@handleBusinessFirstOrderReceived',
        ],

        BusinessOnboardingCompleted::class => [
            SendBusinessOnboardingNotification::class.'@handleBusinessOnboardingCompleted',
        ],

        BusinessSetupStepCompleted::class => [
            SendBusinessOnboardingNotification::class.'@handleBusinessSetupStepCompleted',
        ],

        BusinessTemporarilyClosed::class => [
            SendBusinessOperationNotification::class.'@handleBusinessClosed',
        ],

        BusinessReopened::class => [
            SendBusinessOperationNotification::class.'@handleBusinessReopened',
        ],

        // Order Events (consolidated)
        OrderStatusUpdated::class => [
            SendOrderStatusNotification::class,
        ],

        OrderCancelled::class => [
            SendOrderCancelledNotification::class,
        ],

        // Payment Events (consolidated)
        PaymentStatusChanged::class => [
            SendPaymentNotification::class,
        ],

        // Payout Events
        PayoutProcessed::class => [
            SendPayoutNotification::class.'@handlePayoutProcessed',
        ],

        PayoutFailed::class => [
            SendPayoutNotification::class.'@handlePayoutFailed',
        ],

        // Delivery Events
        DeliveryAssigned::class => [
            SendDeliveryAssignedNotification::class,
        ],

        DeliveryCompleted::class => [
            SendDeliveryCompletedNotification::class,
        ],

        // Driver Events
        DriverOnlineStatusChanged::class => [
            SendDriverStatusNotification::class.'@handleDriverStatusChanged',
        ],

        VehicleBreakdownReported::class => [
            SendDriverStatusNotification::class.'@handleVehicleBreakdown',
        ],

        // Stock Events
        StockLevelLow::class => [
            SendStockLevelAlert::class,
        ],

        StockLevelCritical::class => [
            SendStockLevelCriticalAlert::class,
        ],

        ProductOutOfStock::class => [
            SendStockLevelCriticalAlert::class, // Use same handler for out of stock
        ],

        StockReplenished::class => [
            SendStockLevelAlert::class, // Notify about restocking
        ],

        // Security Events
        SuspiciousActivityDetected::class => [
            SendSecurityAlert::class.'@handleSuspiciousActivity',
        ],

        AccountSuspended::class => [
            SendSecurityAlert::class.'@handleAccountSuspended',
        ],

        // Business Management Events
        FeedbackSubmitted::class => [
            SendBusinessManagementNotification::class.'@handleFeedbackSubmitted',
        ],

        ApiKeyGenerated::class => [
            SendBusinessManagementNotification::class.'@handleApiKeyGenerated',
        ],

        ServiceAreaUpdated::class => [
            SendBusinessManagementNotification::class.'@handleServiceAreaUpdated',
        ],

        StaffAdded::class => [
            SendBusinessManagementNotification::class.'@handleStaffAdded',
        ],

        ProviderStaffAdded::class => [
            SendBusinessManagementNotification::class.'@handleProviderStaffAdded',
        ],

        TeamMemberStatusChanged::class => [
            SendBusinessManagementNotification::class.'@handleTeamMemberStatusChanged',
        ],

        ProviderTeamMemberStatusChanged::class => [
            SendBusinessManagementNotification::class.'@handleProviderTeamMemberStatusChanged',
        ],

        VehicleAdded::class => [
            SendBusinessManagementNotification::class.'@handleVehicleAdded',
        ],

        // Advanced Events
        PricingCalculated::class => [
            SendAdvancedNotifications::class.'@handlePricingCalculated',
        ],

        ProviderOnboardingCompleted::class => [
            SendAdvancedNotifications::class.'@handleProviderOnboardingCompleted',
        ],

        InterstateDeliveryDetected::class => [
            SendAdvancedNotifications::class.'@handleInterstateDeliveryDetected',
        ],

        WebhookDeliveryFailed::class => [
            SendAdvancedNotifications::class.'@handleWebhookDeliveryFailed',
        ],

        ApiRateLimitExceeded::class => [
            SendAdvancedNotifications::class.'@handleApiRateLimitExceeded',
        ],

        CustomPolygonZoneCreated::class => [
            // To be implemented in future phase
        ],

        // Critical Business Events
        DisputeCreated::class => [
            SendCriticalBusinessNotifications::class.'@handleDisputeCreated',
        ],

        ReferralSuccessful::class => [
            SendCriticalBusinessNotifications::class.'@handleReferralSuccessful',
        ],

        FraudDetected::class => [
            SendCriticalBusinessNotifications::class.'@handleFraudDetected',
        ],

        CommissionCalculated::class => [
            SendCriticalBusinessNotifications::class.'@handleCommissionCalculated',
        ],

        PromotionApplied::class => [
            SendCriticalBusinessNotifications::class.'@handlePromotionApplied',
        ],

        // Status Change Events
        UserStatusChanged::class => [
            SendStatusChangeNotifications::class.'@handleUserStatusChanged',
        ],

        BusinessStatusChanged::class => [
            SendStatusChangeNotifications::class.'@handleBusinessStatusChanged',
        ],

        DeliveryProviderStatusChanged::class => [
            SendStatusChangeNotifications::class.'@handleDeliveryProviderStatusChanged',
        ],

        TenantStatusChanged::class => [
            SendStatusChangeNotifications::class.'@handleTenantStatusChanged',
        ],

        // Ad-Hoc Delivery Events
        AdHocDeliveryCreated::class => [
            SendAdHocDeliveryNotifications::class.'@handleAdHocDeliveryCreated',
        ],

        AdHocDeliveryProviderAssigned::class => [
            SendAdHocDeliveryNotifications::class.'@handleAdHocDeliveryProviderAssigned',
        ],

        AdHocDeliveryCompleted::class => [
            SendAdHocDeliveryNotifications::class.'@handleAdHocDeliveryCompleted',
        ],

        // Provider Availability Events
        ProviderAvailabilityChanged::class => [
            SendAdvancedNotifications::class.'@handleProviderAvailabilityChanged',
        ],

        // Inventory Events
        InventoryStockIn::class => [
            SendBusinessManagementNotification::class.'@handleInventoryStockIn',
        ],

        InventoryStockOut::class => [
            SendBusinessManagementNotification::class.'@handleInventoryStockOut',
        ],

        // Purchase Order Events
        PurchaseOrderCreated::class => [
            SendBusinessManagementNotification::class.'@handlePurchaseOrderCreated',
        ],

        // Verification Events
        VerificationSubmitted::class => [
            SendAdvancedNotifications::class.'@handleVerificationSubmitted',
        ],

        VerificationApproved::class => [
            SendAdvancedNotifications::class.'@handleVerificationApproved',
        ],

        // Report Events
        ReportGenerationCompleted::class => [
            SendAdvancedNotifications::class.'@handleReportGenerationCompleted',
        ],

        // AI/ML Events
        AiPredictionGenerated::class => [
            SendAdvancedNotifications::class.'@handleAiPredictionGenerated',
        ],

        // Critical System Events
        DisputeStatusChanged::class => [
            SendCriticalSystemNotifications::class.'@handleDisputeStatusChanged',
        ],

        SupportTicketCreated::class => [
            SendCriticalSystemNotifications::class.'@handleSupportTicketCreated',
        ],

        SupportTicketStatusChanged::class => [
            SendCriticalSystemNotifications::class.'@handleSupportTicketStatusChanged',
        ],

        WebhookStatusChanged::class => [
            SendCriticalSystemNotifications::class.'@handleWebhookStatusChanged',
        ],

        PaymentStatusChanged::class => [
            SendCriticalSystemNotifications::class.'@handlePaymentStatusChanged',
        ],

        PaymentRefunded::class => [
            SendCriticalSystemNotifications::class.'@handlePaymentRefunded',
        ],

        PayoutStatusChanged::class => [
            SendCriticalSystemNotifications::class.'@handlePayoutStatusChanged',
        ],

        VehicleStatusChanged::class => [
            SendCriticalSystemNotifications::class.'@handleVehicleStatusChanged',
        ],

        ReportStatusChanged::class => [
            SendCriticalSystemNotifications::class.'@handleReportStatusChanged',
        ],

        CommissionStatusChanged::class => [
            SendCriticalSystemNotifications::class.'@handleCommissionStatusChanged',
        ],

        UserSubscriptionStatusChanged::class => [
            SendCriticalSystemNotifications::class.'@handleUserSubscriptionStatusChanged',
        ],

        DeliveryRequestStatusChanged::class => [
            SendCriticalSystemNotifications::class.'@handleDeliveryRequestStatusChanged',
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
