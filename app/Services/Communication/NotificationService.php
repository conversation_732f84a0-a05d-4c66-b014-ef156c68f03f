<?php

declare(strict_types=1);

namespace App\Services\Communication;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Delivery\Order;
use App\Models\Delivery\OrderDelivery;
use App\Models\Financial\Payment;
use App\Models\User\TeamInvitation;
use App\Models\User\User;
use App\Notifications\Business\StaffInvitation;
use App\Notifications\BusinessVerificationStatusUpdated;
use App\Notifications\Delivery\OrderDeliveredNotification;
use App\Notifications\DeliveryStatusUpdated;
use App\Notifications\OrderCancelledNotification;
use App\Notifications\OrderShippedNotification;
use App\Notifications\OrderStatusUpdated;
use App\Notifications\PaymentProcessed;
use App\Notifications\SystemAnnouncement;
use App\Notifications\User\DualChannelOtpNotification;
use App\Notifications\User\WelcomeUser;
use App\Services\System\LoggingService;
use Illuminate\Support\Facades\Notification;

/**
 * Simplified Notification Service
 *
 * Uses Laravel's built-in notification system with FCM and Twilio channels.
 * Much simpler than the previous over-engineered approach.
 */
class NotificationService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Send order status update notification.
     */
    public function sendOrderStatusUpdate(Order $order, string $previousStatus, string $newStatus): void
    {
        $customer = $order->customer;

        if (! $customer) {
            return;
        }

        try {
            $customer->notify(new OrderStatusUpdated($order, $previousStatus, $newStatus, $order->business_tenant_id));

            $this->loggingService->logInfo('Order status notification sent', [
                'order_id' => $order->id,
                'customer_id' => $customer->id,
                'previous_status' => $previousStatus,
                'new_status' => $newStatus,
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send order status notification', $e, [
                'order_id' => $order->id,
                'customer_id' => $customer->id,
            ]);
        }
    }

    /**
     * Send payment notification (success or failure).
     */
    public function sendPaymentNotification(Payment $payment): void
    {
        $user = $payment->user;

        if (! $user) {
            return;
        }

        try {
            $user->notify(new PaymentProcessed($payment));

            $this->loggingService->logInfo('Payment notification sent', [
                'payment_id' => $payment->id,
                'user_id' => $user->id,
                'status' => $payment->status,
                'amount' => $payment->amount,
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send payment notification', $e, [
                'payment_id' => $payment->id,
                'user_id' => $user->id,
            ]);
        }
    }

    /**
     * Send notification to multiple users.
     */
    public function sendBulkNotification(array $users, $notification): void
    {
        try {
            Notification::send($users, $notification);

            $this->loggingService->logInfo('Bulk notification sent', [
                'user_count' => count($users),
                'notification_type' => get_class($notification),
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send bulk notification', $e, [
                'user_count' => count($users),
            ]);
        }
    }

    /**
     * Send notification to specific user.
     */
    public function sendToUser(User $user, $notification): void
    {
        try {
            $user->notify($notification);

            $this->loggingService->logInfo('User notification sent', [
                'user_id' => $user->id,
                'notification_type' => get_class($notification),
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send user notification', $e, [
                'user_id' => $user->id,
            ]);
        }
    }

    /**
     * Send welcome notification to new user.
     */
    public function sendWelcomeNotification(User $user, string $accountType = 'customer'): void
    {
        try {
            $user->notify(new WelcomeUser($accountType));

            $this->loggingService->logInfo('Welcome notification sent', [
                'user_id' => $user->id,
                'account_type' => $accountType,
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send welcome notification', $e, [
                'user_id' => $user->id,
            ]);
        }
    }

    /**
     * Send email verification code.
     */
    public function sendEmailVerificationCode(User $user, string $code, int $expiresInMinutes = 30): void
    {
        try {
            // Use DualChannelOtpNotification for consistency
            $user->notify(new DualChannelOtpNotification($code, 'email_verification', $expiresInMinutes));

            $this->loggingService->logInfo('Email verification code sent', [
                'user_id' => $user->id,
                'expires_in_minutes' => $expiresInMinutes,
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send email verification code', $e, [
                'user_id' => $user->id,
            ]);
        }
    }

    /**
     * Send password reset code.
     */
    public function sendPasswordResetCode(User $user, string $code, int $expiresInMinutes = 30): void
    {
        try {
            // Use DualChannelOtpNotification for consistency
            $user->notify(new DualChannelOtpNotification($code, 'password_reset', $expiresInMinutes));

            $this->loggingService->logInfo('Password reset code sent', [
                'user_id' => $user->id,
                'expires_in_minutes' => $expiresInMinutes,
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send password reset code', $e, [
                'user_id' => $user->id,
            ]);
        }
    }

    /**
     * Send staff invitation notification.
     */
    public function sendStaffInvitation(User $newStaff, Business $business, User $invitedBy, string $role, string $temporaryPassword): void
    {
        try {
            $newStaff->notify(new StaffInvitation($business, $invitedBy, $role, $temporaryPassword));

            $this->loggingService->logInfo('Staff invitation sent', [
                'new_staff_id' => $newStaff->id,
                'business_id' => $business->id,
                'invited_by_id' => $invitedBy->id,
                'role' => $role,
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send staff invitation', $e, [
                'new_staff_id' => $newStaff->id,
                'business_id' => $business->id,
            ]);
        }
    }

    /**
     * Send team invitation notification (token-based).
     */
    public function sendTeamInvitation(TeamInvitation $invitation, Business|DeliveryProvider $entity, User $invitedBy): void
    {
        try {
            // Create a temporary user object for the invitation email
            $inviteeUser = new User([
                'email' => $invitation->email,
                'first_name' => $invitation->metadata['first_name'] ?? '',
                'last_name' => $invitation->metadata['last_name'] ?? '',
            ]);

            // Send invitation notification
            $inviteeUser->notify(new \App\Notifications\User\TeamInvitationNotification(
                $invitation,
                $entity,
                $invitedBy
            ));

            $this->loggingService->logInfo('Team invitation sent', [
                'invitation_id' => $invitation->id,
                'email' => $invitation->email,
                'entity_type' => $invitation->metadata['invitation_type'],
                'entity_id' => $entity->id,
                'invited_by_id' => $invitedBy->id,
                'role' => $invitation->role,
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send team invitation', $e, [
                'invitation_id' => $invitation->id,
                'email' => $invitation->email,
            ]);
        }
    }

    /**
     * Send delivery status update notification.
     */
    public function sendDeliveryStatusUpdate(
        OrderDelivery $delivery,
        string $previousStatus,
        string $newStatus,
        ?string $driverName = null,
        ?string $estimatedArrival = null
    ): void {
        $customer = $delivery->order->customer;

        if (! $customer) {
            return;
        }

        try {
            $customer->notify(new DeliveryStatusUpdated($delivery, $previousStatus, $newStatus, $driverName, $estimatedArrival));

            $this->loggingService->logInfo('Delivery status notification sent', [
                'delivery_id' => $delivery->id,
                'customer_id' => $customer->id,
                'previous_status' => $previousStatus,
                'new_status' => $newStatus,
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send delivery status notification', $e, [
                'delivery_id' => $delivery->id,
                'customer_id' => $customer->id,
            ]);
        }
    }

    /**
     * Send business verification status update notification.
     */
    public function sendBusinessVerificationStatusUpdate(Business $business, string $previousStatus, string $newStatus, ?string $reason = null): void
    {
        $owner = $business->user;

        if (! $owner) {
            return;
        }

        try {
            $owner->notify(new BusinessVerificationStatusUpdated($business, $previousStatus, $newStatus, $reason));

            $this->loggingService->logInfo('Business verification status notification sent', [
                'business_id' => $business->id,
                'owner_id' => $owner->id,
                'previous_status' => $previousStatus,
                'new_status' => $newStatus,
                'reason' => $reason,
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send business verification status notification', $e, [
                'business_id' => $business->id,
                'owner_id' => $owner->id,
            ]);
        }
    }

    /**
     * Send system announcement to users.
     */
    public function sendSystemAnnouncement(array $users, string $title, string $message, string $type = 'info', ?string $actionUrl = null, ?string $actionText = null): void
    {
        try {
            Notification::send($users, new SystemAnnouncement($title, $message, $type, $actionUrl, $actionText));

            $this->loggingService->logInfo('System announcement sent', [
                'user_count' => count($users),
                'title' => $title,
                'type' => $type,
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send system announcement', $e, [
                'user_count' => count($users),
            ]);
        }
    }

    /**
     * Send system announcement to all users.
     */
    public function sendSystemAnnouncementToAll(string $title, string $message, string $type = 'info', ?string $actionUrl = null, ?string $actionText = null): void
    {
        try {
            // Get all active users in batches to avoid memory issues
            User::where('is_active', true)
                ->chunk(100, function ($users) use ($title, $message, $type, $actionUrl, $actionText) {
                    Notification::send($users, new SystemAnnouncement($title, $message, $type, $actionUrl, $actionText));
                });

            $totalUsers = User::where('is_active', true)->count();
            $this->loggingService->logInfo('System announcement sent to all users', [
                'total_users' => $totalUsers,
                'title' => $title,
                'type' => $type,
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send system announcement to all users', $e);
        }
    }

    /**
     * Initialize default notification preferences for new user.
     */
    public function initializeUserPreferences(User $user): void
    {
        if (! $user->notification_preferences) {
            $user->update([
                'notification_preferences' => User::getDefaultNotificationPreferences(),
            ]);
        }
    }

    /**
     * Send dual-channel OTP notification (Twilio Verify + Email)
     */
    public function sendDualChannelOtp(User $user, string $otp, string $purpose, int $expiresInMinutes = 10, ?string $actionUrl = null): void
    {
        try {
            $user->notify(new DualChannelOtpNotification($otp, $purpose, $expiresInMinutes, $actionUrl));

            $this->loggingService->logInfo('Dual-channel OTP sent', [
                'user_id' => $user->id,
                'purpose' => $purpose,
                'expires_in_minutes' => $expiresInMinutes,
                'channels' => 'email+twilio_verify',
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send dual-channel OTP', $e, [
                'user_id' => $user->id,
                'purpose' => $purpose,
            ]);
        }
    }

    /**
     * Send order shipped notification (SMS + Email + Push)
     */
    public function sendOrderShippedNotification(Order $order, ?string $driverName = null, ?string $estimatedDeliveryTime = null): void
    {
        try {
            $customer = $order->customer;
            if (! $customer) {
                throw new \Exception('Order has no customer');
            }

            $customer->notify(new OrderShippedNotification(
                order: $order,
                trackingUrl: url("/orders/{$order->id}/track"),
                estimatedDeliveryTime: $estimatedDeliveryTime,
                driverName: $driverName,
                driverPhone: $order->delivery?->driver?->phone_number
            ));

            $this->loggingService->logInfo('Order shipped notification sent', [
                'order_id' => $order->id,
                'customer_id' => $customer->id,
                'channels' => 'sms+email+push',
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send order shipped notification', $e, [
                'order_id' => $order->id,
            ]);
        }
    }

    /**
     * Send order delivered notification (SMS + Email + Push)
     */
    public function sendOrderDeliveredNotification(Order $order, ?string $deliveryNotes = null): void
    {
        try {
            $customer = $order->customer;
            if (! $customer) {
                throw new \Exception('Order has no customer');
            }

            $customer->notify(new OrderDeliveredNotification(
                order: $order,
                deliveredAt: now(),
                deliveryNotes: $deliveryNotes,
                ratingUrl: url("/orders/{$order->id}/rate"),
                driverName: $order->delivery?->driver?->full_name
            ));

            $this->loggingService->logInfo('Order delivered notification sent', [
                'order_id' => $order->id,
                'customer_id' => $customer->id,
                'channels' => 'sms+email+push',
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send order delivered notification', $e, [
                'order_id' => $order->id,
            ]);
        }
    }

    /**
     * Send order cancelled notification (Email + Push only)
     */
    public function sendOrderCancelledNotification(Order $order, string $reason, ?string $cancelledBy = null): void
    {
        try {
            $customer = $order->customer;
            if (! $customer) {
                throw new \Exception('Order has no customer');
            }

            $customer->notify(new OrderCancelledNotification(
                order: $order,
                reason: $reason,
                cancelledBy: $cancelledBy
            ));

            $this->loggingService->logInfo('Order cancelled notification sent', [
                'order_id' => $order->id,
                'customer_id' => $customer->id,
                'reason' => $reason,
                'channels' => 'email+push_only',
            ]);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send order cancelled notification', $e, [
                'order_id' => $order->id,
            ]);
        }
    }

    // Legacy methods for backward compatibility
    public function sendPaymentSuccessNotification(Payment $payment): void
    {
        $this->sendPaymentNotification($payment);
    }

    public function sendPaymentFailureNotification(Payment $payment): void
    {
        $this->sendPaymentNotification($payment);
    }
}
