<?php

declare(strict_types=1);

namespace App\Services\Tenant;

use App\Events\Tenant\TenantStaffAdded;
use App\Events\Tenant\TenantStatusChanged;
use App\Events\Tenant\TenantTeamMemberStatusChanged;
use App\Models\Business\Business;
use App\Models\Business\BusinessTeamMember;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use App\Services\System\LoggingService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * Unified Tenant Management Service
 *
 * Handles common team management operations for both businesses and delivery providers.
 * Eliminates code duplication between BusinessTeamController and ProviderTeamController.
 */
class TenantManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Create a staff member for a tenant (business or provider)
     */
    public function createStaffMember(
        Business|DeliveryProvider $tenant,
        array $staffData,
        User $createdBy,
        bool $autoVerifyEmail = false,
        bool $autoVerifyPhone = false
    ): array {
        try {
            // Generate temporary password
            $temporaryPassword = $this->generateTemporaryPassword();

            // Create user
            $user = User::create([
                'first_name' => $staffData['first_name'],
                'last_name' => $staffData['last_name'],
                'email' => $staffData['email'],
                'phone_number' => $staffData['phone_number'] ?? null,
                'password' => Hash::make($temporaryPassword),
                'user_type' => $this->getUserTypeForTenant($tenant),
                'tenant_id' => tenant()?->id,
                'timezone' => 'Africa/Lagos',
                'is_active' => true,
                'email_verified_at' => $autoVerifyEmail ? now() : null,
                'phone_verified_at' => ($autoVerifyPhone && isset($staffData['phone_number'])) ? now() : null,
            ]);

            // Create team member relationship
            $this->createTeamMemberRelationship($tenant, $user, $staffData['role']);

            // Assign role using Bouncer
            $this->assignBouncerRole($user, $staffData['role'], $tenant);

            // Dispatch unified event
            TenantStaffAdded::dispatch(
                $user,
                $tenant,
                $createdBy,
                $staffData['role'],
                $temporaryPassword,
                [
                    'auto_verify_email' => $autoVerifyEmail,
                    'auto_verify_phone' => $autoVerifyPhone,
                ]
            );

            $this->loggingService->logInfo('Staff member created successfully', [
                'tenant_type' => $this->getTenantType($tenant),
                'tenant_id' => $tenant->id,
                'staff_member_id' => $user->id,
                'role' => $staffData['role'],
                'created_by_id' => $createdBy->id,
            ]);

            return [
                'user' => $user,
                'temporary_password' => $temporaryPassword,
                'credentials' => [
                    'email' => $user->email,
                    'temporary_password' => $temporaryPassword,
                    'login_url' => $this->getLoginUrl($tenant),
                ],
            ];

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to create staff member', $e, [
                'tenant_type' => $this->getTenantType($tenant),
                'tenant_id' => $tenant->id,
                'staff_data' => $staffData,
            ]);
            throw $e;
        }
    }

    /**
     * Change team member status (activate, deactivate, etc.)
     */
    public function changeTeamMemberStatus(
        User $teamMember,
        Business|DeliveryProvider $tenant,
        string $action,
        User $changedBy,
        ?string $newRole = null,
        ?string $message = null
    ): void {
        try {
            // Update team member status based on action
            $this->updateTeamMemberStatus($tenant, $teamMember, $action, $newRole);

            // Update user status if needed
            if ($action === 'activated') {
                $teamMember->update(['is_active' => true]);
            } elseif ($action === 'deactivated') {
                $teamMember->update(['is_active' => false]);
            }

            // Dispatch unified event
            TenantTeamMemberStatusChanged::dispatch(
                $teamMember,
                $tenant,
                $changedBy,
                $action,
                $newRole,
                $message
            );

            $this->loggingService->logInfo('Team member status changed', [
                'tenant_type' => $this->getTenantType($tenant),
                'tenant_id' => $tenant->id,
                'team_member_id' => $teamMember->id,
                'action' => $action,
                'new_role' => $newRole,
                'changed_by_id' => $changedBy->id,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to change team member status', $e, [
                'tenant_type' => $this->getTenantType($tenant),
                'tenant_id' => $tenant->id,
                'team_member_id' => $teamMember->id,
                'action' => $action,
            ]);
            throw $e;
        }
    }

    /**
     * Change tenant status (business or provider)
     */
    public function changeTenantStatus(
        Business|DeliveryProvider $tenant,
        string $newStatus,
        ?string $reason = null,
        ?User $changedBy = null
    ): void {
        try {
            $oldStatus = $tenant->status;

            // Update tenant status
            $tenant->update(['status' => $newStatus]);

            // Dispatch unified event
            TenantStatusChanged::dispatch(
                $tenant,
                $oldStatus,
                $newStatus,
                $reason,
                $changedBy
            );

            $this->loggingService->logInfo('Tenant status changed', [
                'tenant_type' => $this->getTenantType($tenant),
                'tenant_id' => $tenant->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'reason' => $reason,
                'changed_by_id' => $changedBy?->id,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to change tenant status', $e, [
                'tenant_type' => $this->getTenantType($tenant),
                'tenant_id' => $tenant->id,
                'new_status' => $newStatus,
            ]);
            throw $e;
        }
    }

    /**
     * Generate a secure temporary password
     */
    private function generateTemporaryPassword(): string
    {
        return 'DN' . Str::upper(Str::random(2)) . rand(1000, 9999) . '!';
    }

    /**
     * Get user type based on tenant type
     */
    private function getUserTypeForTenant(Business|DeliveryProvider $tenant): string
    {
        return $tenant instanceof Business ? 'business_owner' : 'delivery_provider';
    }

    /**
     * Get tenant type string
     */
    private function getTenantType(Business|DeliveryProvider $tenant): string
    {
        return $tenant instanceof Business ? 'business' : 'provider';
    }

    /**
     * Create team member relationship based on tenant type
     */
    private function createTeamMemberRelationship(
        Business|DeliveryProvider $tenant,
        User $user,
        string $role
    ): void {
        if ($tenant instanceof Business) {
            BusinessTeamMember::create([
                'tenant_id' => tenant()?->id,
                'business_id' => $tenant->id,
                'user_id' => $user->id,
                'role' => $role,
                'joined_at' => now(),
            ]);
        } else {
            // For DeliveryProvider - implement when ProviderTeamMember model exists
            // ProviderTeamMember::create([...]);
        }
    }

    /**
     * Assign Bouncer role based on tenant type and role
     */
    private function assignBouncerRole(User $user, string $role, Business|DeliveryProvider $tenant): void
    {
        if ($tenant instanceof Business) {
            $bouncerRole = match ($role) {
                'admin' => 'business-admin',
                'manager' => 'business-manager',
                'staff' => 'business-staff',
                default => 'business-staff'
            };
        } else {
            $bouncerRole = match ($role) {
                'admin' => 'provider-admin',
                'manager' => 'provider-manager',
                'driver' => 'provider-driver',
                'staff' => 'provider-staff',
                default => 'provider-staff'
            };
        }

        $user->assign($bouncerRole);
    }

    /**
     * Update team member status in database
     */
    private function updateTeamMemberStatus(
        Business|DeliveryProvider $tenant,
        User $teamMember,
        string $action,
        ?string $newRole = null
    ): void {
        if ($tenant instanceof Business) {
            $teamMemberRecord = BusinessTeamMember::where('business_id', $tenant->id)
                ->where('user_id', $teamMember->id)
                ->first();

            if ($teamMemberRecord && $newRole) {
                $teamMemberRecord->update(['role' => $newRole]);
            }
        } else {
            // For DeliveryProvider - implement when ProviderTeamMember model exists
        }
    }

    /**
     * Get login URL based on tenant type
     */
    private function getLoginUrl(Business|DeliveryProvider $tenant): string
    {
        if ($tenant instanceof Business) {
            return config('app.tenant_url', 'https://business.deliverynexus.com') . '/login';
        } else {
            return config('app.tenant_url', 'https://provider.deliverynexus.com') . '/login';
        }
    }
}
