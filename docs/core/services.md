# DeliveryNexus Services Documentation

**Last Updated**: January 2025  
**Total Services**: 50+ service classes implementing business logic  
**Architecture**: Service layer pattern with dependency injection

## Table of Contents
1. [Core Business Services](#core-business-services)
2. [Authentication & User Services](#authentication--user-services)
3. [Payment & Financial Services](#payment--financial-services)
4. [Delivery & Logistics Services](#delivery--logistics-services)
5. [Communication Services](#communication-services)
6. [System & Infrastructure Services](#system--infrastructure-services)
7. [Integration Services](#integration-services)
8. [Service Patterns](#service-patterns)

---

## Core Business Services

### BusinessService
**File**: `app/Services/BusinessService.php`  
**Purpose**: Manages business operations, onboarding, and lifecycle

**Key Methods**:
- `createBusiness(array $data)`: Create new business with tenant
- `updateBusiness(Business $business, array $data)`: Update business information
- `verifyBusiness(Business $business)`: Handle business verification process
- `suspendBusiness(Business $business, string $reason)`: Suspend business operations
- `calculateRevenue(Business $business, ?Carbon $from, ?Carbon $to)`: Calculate business revenue
- `getBusinessMetrics(Business $business)`: Get comprehensive business metrics
- `handleOnboarding(Business $business, array $steps)`: Manage onboarding process

**Dependencies**:
- `TenantService`: Tenant management
- `NotificationService`: Business notifications
- `FileStorageService`: Document handling

**Usage Example**:
```php
$businessService = app(BusinessService::class);
$business = $businessService->createBusiness([
    'business_name' => 'Test Restaurant',
    'business_type' => BusinessType::FOOD,
    'user_id' => $userId,
]);
```

### ProductService
**File**: `app/Services/ProductService.php`  
**Purpose**: Manages product catalog, inventory, and pricing

**Key Methods**:
- `createProduct(Business $business, array $data)`: Create new product
- `updateProduct(Product $product, array $data)`: Update product information
- `manageInventory(Product $product, int $quantity, string $type)`: Handle inventory changes
- `calculatePricing(Product $product, ?array $modifiers)`: Calculate dynamic pricing
- `bulkImport(Business $business, array $products)`: Bulk product import
- `generateVariants(Product $product, array $options)`: Generate product variants
- `searchProducts(array $criteria)`: Advanced product search

**Dependencies**:
- `InventoryService`: Stock management
- `ImageStorageService`: Product images
- `SearchService`: Product indexing

### OrderService
**File**: `app/Services/OrderService.php`  
**Purpose**: Handles order lifecycle, processing, and fulfillment

**Key Methods**:
- `createOrder(User $user, array $orderData)`: Create new order
- `processOrder(Order $order)`: Process order payment and fulfillment
- `updateOrderStatus(Order $order, OrderStatus $status)`: Update order status
- `calculateOrderTotal(array $items, ?array $modifiers)`: Calculate order totals
- `assignDelivery(Order $order)`: Assign delivery provider
- `cancelOrder(Order $order, string $reason)`: Cancel order with refund
- `scheduleOrder(Order $order, Carbon $scheduledFor)`: Schedule future order

**Dependencies**:
- `PaymentService`: Payment processing
- `DeliveryService`: Delivery assignment
- `NotificationService`: Order notifications
- `InventoryService`: Stock updates

---

## Authentication & User Services

### AuthService
**File**: `app/Services/AuthService.php`  
**Purpose**: Handles authentication, registration, and user management

**Key Methods**:
- `register(array $userData)`: Register new user
- `login(string $email, string $password)`: Authenticate user
- `logout(User $user)`: Logout user and revoke tokens
- `sendVerificationCode(User $user, string $channel)`: Send verification code
- `verifyCode(User $user, string $code, string $channel)`: Verify user code
- `resetPassword(string $email)`: Initiate password reset
- `changePassword(User $user, string $newPassword)`: Change user password
- `refreshToken(string $refreshToken)`: Refresh authentication token

**Dependencies**:
- `SmsService`: SMS verification
- `EmailService`: Email verification
- `NotificationService`: Auth notifications

### UserService
**File**: `app/Services/UserService.php`  
**Purpose**: Manages user profiles, preferences, and relationships

**Key Methods**:
- `updateProfile(User $user, array $data)`: Update user profile
- `manageAddresses(User $user, array $addresses)`: Manage user addresses
- `setNotificationPreferences(User $user, array $preferences)`: Set notification preferences
- `linkSocialAccount(User $user, string $provider, array $data)`: Link OAuth account
- `deactivateUser(User $user)`: Deactivate user account
- `getUserMetrics(User $user)`: Get user activity metrics

### RoleService
**File**: `app/Services/RoleService.php`  
**Purpose**: Manages roles, permissions, and access control using Bouncer

**Key Methods**:
- `assignRole(User $user, string $role, ?string $scope)`: Assign role to user
- `revokeRole(User $user, string $role, ?string $scope)`: Revoke user role
- `grantAbility(User $user, string $ability, ?Model $model)`: Grant specific ability
- `checkPermission(User $user, string $ability, ?Model $model)`: Check user permission
- `getUserRoles(User $user, ?string $scope)`: Get user roles
- `syncTenantPermissions(User $user, string $tenantId)`: Sync tenant-specific permissions

---

## Payment & Financial Services

### PaymentService
**File**: `app/Services/PaymentService.php`  
**Purpose**: Handles payment processing, refunds, and financial transactions

**Key Methods**:
- `processPayment(Order $order, PaymentMethod $method)`: Process order payment
- `refundPayment(Payment $payment, ?float $amount)`: Process payment refund
- `createPaymentMethod(User $user, array $data)`: Create payment method
- `validatePayment(array $paymentData)`: Validate payment data
- `handleWebhook(string $gateway, array $payload)`: Handle payment webhooks
- `calculateFees(float $amount, string $currency)`: Calculate payment fees
- `getPaymentHistory(User $user, ?array $filters)`: Get user payment history

**Dependencies**:
- `PaystackService`: Paystack integration
- `NotificationService`: Payment notifications
- `AuditService`: Payment logging

### PaystackService
**File**: `app/Services/PaystackService.php`  
**Purpose**: Paystack payment gateway integration

**Key Methods**:
- `initializeTransaction(array $data)`: Initialize Paystack transaction
- `verifyTransaction(string $reference)`: Verify transaction status
- `chargeCard(array $cardData, float $amount)`: Charge saved card
- `createCustomer(User $user)`: Create Paystack customer
- `saveCard(User $user, string $authCode)`: Save card for future use
- `processRefund(string $transactionId, float $amount)`: Process refund
- `handleWebhook(array $payload)`: Handle Paystack webhooks

### CommissionService
**File**: `app/Services/CommissionService.php`  
**Purpose**: Calculates and manages platform commissions

**Key Methods**:
- `calculateCommission(Order $order)`: Calculate order commission
- `processCommissionPayment(Commission $commission)`: Process commission payout
- `getCommissionRates(string $type, ?Model $entity)`: Get applicable commission rates
- `generateCommissionReport(array $filters)`: Generate commission reports
- `adjustCommission(Commission $commission, float $adjustment)`: Adjust commission amount

---

## Delivery & Logistics Services

### DeliveryService
**File**: `app/Services/DeliveryService.php`  
**Purpose**: Manages delivery assignment, tracking, and optimization

**Key Methods**:
- `assignDelivery(Order $order)`: Assign delivery to provider
- `calculateDeliveryFee(Address $pickup, Address $delivery)`: Calculate delivery cost
- `trackDelivery(Delivery $delivery)`: Get real-time delivery tracking
- `updateDeliveryStatus(Delivery $delivery, DeliveryStatus $status)`: Update delivery status
- `optimizeRoute(array $deliveries)`: Optimize delivery routes
- `findNearestProviders(Address $address, ?array $criteria)`: Find nearby providers
- `scheduleDelivery(Delivery $delivery, Carbon $scheduledTime)`: Schedule delivery

**Dependencies**:
- `GeolocationService`: Location services
- `RouteOptimizationService`: Route planning
- `NotificationService`: Delivery notifications

### GeolocationService
**File**: `app/Services/GeolocationService.php`  
**Purpose**: Handles location services, geocoding, and distance calculations

**Key Methods**:
- `geocodeAddress(string $address)`: Convert address to coordinates
- `reverseGeocode(float $lat, float $lng)`: Convert coordinates to address
- `calculateDistance(Address $from, Address $to)`: Calculate distance between addresses
- `findNearbyLocations(float $lat, float $lng, float $radius)`: Find nearby locations
- `validateCoordinates(float $lat, float $lng)`: Validate GPS coordinates
- `getTimezone(float $lat, float $lng)`: Get timezone for coordinates

**Dependencies**:
- Google Maps Platform API
- PostGIS database functions

### RouteOptimizationService
**File**: `app/Services/RouteOptimizationService.php`  
**Purpose**: Optimizes delivery routes and provides ETA calculations

**Key Methods**:
- `optimizeMultipleDeliveries(array $deliveries)`: Optimize multiple delivery routes
- `calculateETA(Address $from, Address $to, ?Carbon $departureTime)`: Calculate estimated time
- `findOptimalRoute(array $waypoints)`: Find optimal route through waypoints
- `considerTrafficConditions(Route $route)`: Factor in traffic conditions
- `generateRouteInstructions(Route $route)`: Generate turn-by-turn directions

---

## Communication Services

### NotificationService
**File**: `app/Services/NotificationService.php`  
**Purpose**: Manages multi-channel notifications and messaging

**Key Methods**:
- `send(Notifiable $notifiable, Notification $notification)`: Send notification
- `sendBulk(array $notifiables, Notification $notification)`: Send bulk notifications
- `schedule(Notifiable $notifiable, Notification $notification, Carbon $when)`: Schedule notification
- `markAsRead(Notification $notification)`: Mark notification as read
- `getUserPreferences(User $user)`: Get user notification preferences
- `updatePreferences(User $user, array $preferences)`: Update notification preferences
- `getDeliveryStatus(Notification $notification)`: Get delivery status across channels

**Dependencies**:
- `SmsService`: SMS delivery
- `EmailService`: Email delivery
- `PushNotificationService`: Push notifications
- `WhatsAppService`: WhatsApp messaging

### SmsService
**File**: `app/Services/SmsService.php`  
**Purpose**: SMS messaging via Twilio integration

**Key Methods**:
- `sendSms(string $to, string $message)`: Send SMS message
- `sendVerificationCode(string $phoneNumber)`: Send verification code
- `verifyCode(string $phoneNumber, string $code)`: Verify SMS code
- `sendBulkSms(array $recipients, string $message)`: Send bulk SMS
- `getDeliveryStatus(string $messageId)`: Get SMS delivery status
- `validatePhoneNumber(string $phoneNumber)`: Validate phone number format

**Dependencies**:
- Twilio Verify API
- Twilio Messaging API

### EmailService
**File**: `app/Services/EmailService.php`  
**Purpose**: Email messaging via ZeptoMail SMTP

**Key Methods**:
- `sendEmail(string $to, string $subject, string $content)`: Send email
- `sendTemplateEmail(string $to, string $template, array $data)`: Send template email
- `sendBulkEmail(array $recipients, string $subject, string $content)`: Send bulk email
- `trackEmailOpens(string $messageId)`: Track email opens
- `handleBounces(array $bounceData)`: Handle email bounces
- `validateEmailAddress(string $email)`: Validate email format

**Dependencies**:
- ZeptoMail SMTP
- Laravel Mail system

### PushNotificationService
**File**: `app/Services/PushNotificationService.php`  
**Purpose**: Push notifications via Firebase Cloud Messaging

**Key Methods**:
- `sendPushNotification(User $user, array $data)`: Send push notification
- `sendToTopic(string $topic, array $data)`: Send to FCM topic
- `subscribeToTopic(User $user, string $topic)`: Subscribe user to topic
- `unsubscribeFromTopic(User $user, string $topic)`: Unsubscribe from topic
- `updateFcmToken(User $user, string $token)`: Update user FCM token
- `sendBulkNotifications(array $users, array $data)`: Send bulk notifications

**Dependencies**:
- Firebase Cloud Messaging
- FCM token management

---

## System & Infrastructure Services

### CacheService
**File**: `app/Services/CacheService.php`  
**Purpose**: Tenant-aware caching with intelligent invalidation

**Key Methods**:
- `remember(string $key, int $ttl, Closure $callback)`: Cache with callback
- `put(string $key, mixed $value, int $ttl)`: Store cache value
- `get(string $key, mixed $default)`: Retrieve cache value
- `forget(string $key)`: Remove cache entry
- `flush(?string $pattern)`: Flush cache by pattern
- `warmCache(array $keys)`: Warm cache with data
- `getTenantKey(string $key)`: Get tenant-specific cache key

**Dependencies**:
- Redis cache store
- Tenant context

### SearchService
**File**: `app/Services/SearchService.php`  
**Purpose**: Meilisearch integration for full-text search

**Key Methods**:
- `search(string $query, array $filters, string $index)`: Perform search
- `indexModel(Model $model)`: Index single model
- `bulkIndex(string $modelClass, array $ids)`: Bulk index models
- `removeFromIndex(Model $model)`: Remove from search index
- `updateSearchableData(Model $model)`: Update searchable data
- `getSearchSuggestions(string $query)`: Get search suggestions
- `configureIndex(string $index, array $settings)`: Configure search index

**Dependencies**:
- Meilisearch server
- Laravel Scout

### FileStorageService
**File**: `app/Services/FileStorageService.php`  
**Purpose**: File storage and management via Cloudflare R2

**Key Methods**:
- `store(UploadedFile $file, string $path)`: Store uploaded file
- `storeAs(UploadedFile $file, string $path, string $name)`: Store with custom name
- `delete(string $path)`: Delete stored file
- `exists(string $path)`: Check if file exists
- `url(string $path)`: Get file URL
- `temporaryUrl(string $path, int $minutes)`: Get temporary URL
- `copy(string $from, string $to)`: Copy file

**Dependencies**:
- Cloudflare R2 storage
- Laravel Filesystem

### ImageStorageService
**File**: `app/Services/ImageStorageService.php`  
**Purpose**: Image processing and storage with HEIF/HEIC support

**Key Methods**:
- `storeImage(UploadedFile $image, string $path)`: Store and process image
- `resize(string $imagePath, int $width, int $height)`: Resize image
- `generateThumbnails(string $imagePath, array $sizes)`: Generate thumbnails
- `convertFormat(string $imagePath, string $format)`: Convert image format
- `optimizeImage(string $imagePath)`: Optimize image size
- `extractMetadata(string $imagePath)`: Extract image metadata
- `validateImage(UploadedFile $file)`: Validate image file

**Dependencies**:
- Intervention Image
- HEIF/HEIC conversion libraries
- Cloudflare R2 storage

### TenantManagementService
**File**: `app/Services/Tenant/TenantManagementService.php`
**Purpose**: Unified tenant management operations for businesses and delivery providers

**Key Methods**:
- `createStaffMember()`: Create staff for any tenant type
- `changeTeamMemberStatus()`: Update team member status
- `changeTenantStatus()`: Update tenant status
- Polymorphic support for Business|DeliveryProvider

**Dependencies**:
- PasswordHelper for secure password generation
- Unified events (TenantStaffAdded, TenantTeamMemberStatusChanged)
- LoggingService for comprehensive audit trails

### AuditService
**File**: `app/Services/AuditService.php`
**Purpose**: Comprehensive audit logging and compliance tracking

**Key Methods**:
- `logAction(User $user, string $action, Model $model, array $changes)`: Log user action
- `logSystemEvent(string $event, array $data)`: Log system event
- `logSecurityEvent(string $event, ?User $user, array $context)`: Log security event
- `getAuditTrail(Model $model)`: Get model audit trail
- `generateComplianceReport(array $filters)`: Generate compliance report
- `cleanupOldLogs(int $daysToKeep)`: Cleanup old audit logs
- `exportAuditData(array $filters, string $format)`: Export audit data

**Dependencies**:
- Database logging
- File export capabilities

### MonitoringService
**File**: `app/Services/MonitoringService.php`
**Purpose**: Application performance and health monitoring

**Key Methods**:
- `recordMetric(string $name, float $value, array $tags)`: Record performance metric
- `trackRequest(Request $request, Response $response)`: Track API request
- `monitorDatabasePerformance()`: Monitor database queries
- `checkSystemHealth()`: Perform health checks
- `alertOnThreshold(string $metric, float $threshold)`: Set up alerts
- `generatePerformanceReport()`: Generate performance report
- `getSystemMetrics()`: Get current system metrics

**Dependencies**:
- Laravel Pulse
- Database monitoring
- System resource monitoring

---

## Integration Services

### WhatsAppService
**File**: `app/Services/WhatsAppService.php`
**Purpose**: WhatsApp Business API integration for messaging

**Key Methods**:
- `sendTextMessage(string $to, string $message)`: Send text message
- `sendTemplateMessage(string $to, string $template, array $params)`: Send template message
- `sendMediaMessage(string $to, string $mediaUrl, string $caption)`: Send media message
- `createTemplate(string $name, string $content, string $category)`: Create message template
- `getTemplateStatus(string $templateId)`: Get template approval status
- `handleWebhook(array $payload)`: Handle WhatsApp webhooks
- `validatePhoneNumber(string $phoneNumber)`: Validate WhatsApp number

**Dependencies**:
- WhatsApp Business API
- Webhook handling

### GoogleMapsService
**File**: `app/Services/GoogleMapsService.php`
**Purpose**: Google Maps Platform integration for location services

**Key Methods**:
- `geocode(string $address)`: Geocode address to coordinates
- `reverseGeocode(float $lat, float $lng)`: Reverse geocode coordinates
- `calculateRoute(array $waypoints)`: Calculate route between points
- `getDistanceMatrix(array $origins, array $destinations)`: Get distance matrix
- `findPlaces(string $query, float $lat, float $lng)`: Find nearby places
- `validateAddress(string $address)`: Validate address format
- `getTimezone(float $lat, float $lng)`: Get timezone for location

**Dependencies**:
- Google Maps Geocoding API
- Google Maps Directions API
- Google Maps Places API

### QrCodeService
**File**: `app/Services/QrCodeService.php`
**Purpose**: QR code generation for business marketing and tracking

**Key Methods**:
- `generateBusinessQr(Business $business, string $style)`: Generate business QR code
- `generateCategoryQr(ProductCategory $category, string $style)`: Generate category QR code
- `generateProductQr(Product $product, string $style)`: Generate product QR code
- `generateBulkQrs(array $items, string $style)`: Generate multiple QR codes
- `customizeQrStyle(array $options)`: Customize QR code appearance
- `trackQrScan(string $qrId, Request $request)`: Track QR code scans
- `getQrAnalytics(string $qrId)`: Get QR code scan analytics

**Dependencies**:
- QR code generation library
- Analytics tracking
- Image processing

---

## Service Patterns

### Dependency Injection

All services use Laravel's service container for dependency injection:

```php
class OrderService
{
    public function __construct(
        private PaymentService $paymentService,
        private DeliveryService $deliveryService,
        private NotificationService $notificationService,
        private InventoryService $inventoryService
    ) {}
}
```

### Service Provider Registration

Services are registered in `AppServiceProvider`:

```php
public function register(): void
{
    $this->app->singleton(BusinessService::class);
    $this->app->singleton(PaymentService::class);
    $this->app->singleton(DeliveryService::class);
    // ... other services
}
```

### Interface Contracts

Critical services implement contracts for testability:

```php
interface PaymentServiceInterface
{
    public function processPayment(Order $order, PaymentMethod $method): Payment;
    public function refundPayment(Payment $payment, ?float $amount): PaymentRefund;
}

class PaymentService implements PaymentServiceInterface
{
    // Implementation
}
```

### Error Handling

Services use consistent error handling patterns:

```php
public function processPayment(Order $order, PaymentMethod $method): Payment
{
    try {
        // Payment processing logic
        return $payment;
    } catch (PaymentException $e) {
        Log::error('Payment failed', [
            'order_id' => $order->id,
            'error' => $e->getMessage()
        ]);
        throw new PaymentProcessingException('Payment could not be processed');
    }
}
```

### Event Integration

Services integrate with Laravel's event system:

```php
public function createOrder(User $user, array $orderData): Order
{
    $order = Order::create($orderData);

    // Dispatch events
    OrderCreated::dispatch($order);

    return $order;
}
```

### Caching Integration

Services use caching for performance optimization:

```php
public function getBusinessMetrics(Business $business): array
{
    return Cache::remember(
        "business_metrics_{$business->id}",
        3600,
        fn() => $this->calculateMetrics($business)
    );
}
```

### Transaction Management

Services handle database transactions appropriately:

```php
public function processOrder(Order $order): void
{
    DB::transaction(function () use ($order) {
        $this->updateInventory($order);
        $this->processPayment($order);
        $this->assignDelivery($order);
        $this->sendNotifications($order);
    });
}
```

---

**Total Services**: 50+ service classes providing comprehensive business logic separation, dependency injection, and maintainable architecture across the entire DeliveryNexus platform.
