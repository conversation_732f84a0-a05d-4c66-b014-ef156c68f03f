# DeliveryNexus Notification System Guide

## 📋 Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Creating Events](#creating-events)
4. [Creating Listeners](#creating-listeners)
5. [Creating Notifications](#creating-notifications)
6. [Creating Mail Templates](#creating-mail-templates)
7. [Event-Listener Registration](#event-listener-registration)
8. [Multi-Channel Strategy](#multi-channel-strategy)
9. [Testing](#testing)
10. [Best Practices](#best-practices)

## 🎯 Overview

DeliveryNexus uses a comprehensive event-driven notification system that supports:
- **Multi-channel delivery**: Email, Push Notifications, SMS (strategic)
- **Real-time broadcasting**: Laravel Reverb WebSockets
- **Queue processing**: Redis-backed queues for scalability
- **Multi-tenancy**: Tenant-aware notifications
- **Consolidated events**: Single events handle multiple status changes for better maintainability
- **Direct Laravel notifications**: No redundant NotificationService wrapper for event-driven notifications

## 🏗️ Architecture

### Core Components
```
Event → Listener → Direct Laravel Notification → Channels (Email/Push/SMS)
  ↓
Broadcasting (WebSocket) + Queue Processing
```

### Key Classes
- **Events**: `app/Events/` - Define what happened (consolidated for status changes)
- **Listeners**: `app/Listeners/` - Handle events and trigger notifications directly
- **Notifications**: `app/Notifications/` - Define notification content
- **Mail Templates**: `resources/views/emails/` - Email layouts
- **NotificationService**: `app/Services/Communication/NotificationService.php` - Utility functions only (OTP, admin features)

### Architecture Principles
- **Event-Driven**: All business notifications go through events
- **Consolidated Events**: Single events handle multiple status changes (e.g., OrderStatusUpdated, PaymentStatusChanged)
- **Direct Notifications**: Event listeners call `$user->notify()` directly, no service wrapper
- **Utility Service**: NotificationService only for admin features, OTP, and system utilities

## 🔥 Creating Events

### 1. Generate Event Class
```bash
php artisan make:event OrderStatusUpdated
```

### 2. Consolidated Event Structure
```php
<?php

declare(strict_types=1);

namespace App\Events\Delivery;

use App\Models\Delivery\Order;
use App\Models\User\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderStatusUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly Order $order,
        public readonly string $previousStatus,
        public readonly string $newStatus,
        public readonly ?string $reason = null,
        public readonly ?User $changedBy = null,
        public readonly array $metadata = []
    ) {}

    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("customer.{$this->order->customer_id}"),
            new PrivateChannel("business.{$this->order->business_tenant_id}"),
        ];
    }

    public function broadcastWith(): array
    {
        return [
            'order_id' => $this->order->id,
            'order_reference' => $this->order->order_reference,
            'previous_status' => $this->previousStatus,
            'new_status' => $this->newStatus,
            'reason' => $this->reason,
            'changed_by' => $this->changedBy?->id,
            'changed_at' => now()->toISOString(),
        ];
    }

    public function broadcastAs(): string
    {
        return 'order.status.updated';
    }
}
```

### 3. Consolidated Event Requirements
- ✅ Implement `ShouldBroadcast` for real-time updates
- ✅ Use `readonly` properties for immutability
- ✅ Include status change information (old/new status, reason, changed by)
- ✅ Define broadcast channels for multi-tenancy
- ✅ Use descriptive broadcast names
- ✅ Handle multiple status changes in single event (reduces redundancy)

## 🎧 Creating Listeners

### 1. Generate Listener Class
```bash
php artisan make:listener SendOrderStatusNotification
```

### 2. Consolidated Listener Structure
```php
<?php

declare(strict_types=1);

namespace App\Listeners\Delivery;

use App\Events\Delivery\OrderStatusUpdated;
use App\Notifications\Delivery\OrderStatusUpdatedNotification;
use App\Services\System\LoggingService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendOrderStatusNotification implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    public function handle(OrderStatusUpdated $event): void
    {
        try {
            // Use direct Laravel notifications - no service wrapper
            $customer = $event->order->customer;
            if ($customer) {
                $customer->notify(new OrderStatusUpdatedNotification(
                    $event->order,
                    $event->previousStatus,
                    $event->newStatus,
                    $event->reason
                ));
            }

            // Notify business owner
            $businessOwner = $event->order->business->owner;
            if ($businessOwner) {
                $businessOwner->notify(new OrderStatusUpdatedNotification(
                    $event->order,
                    $event->previousStatus,
                    $event->newStatus,
                    $event->reason
                ));
            }

            $this->loggingService->logInfo('Order status notification sent', [
                'order_id' => $event->order->id,
                'previous_status' => $event->previousStatus,
                'new_status' => $event->newStatus,
                'customer_id' => $customer?->id,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send order status notification', $e, [
                'order_id' => $event->order->id,
                'status_change' => "{$event->previousStatus} → {$event->newStatus}",
            ]);
            throw $e;
        }
    }
}
```

### 3. Consolidated Listener Best Practices
- ✅ Implement `ShouldQueue` for async processing
- ✅ Use dependency injection for services
- ✅ Handle exceptions gracefully with detailed logging
- ✅ Call `$user->notify()` directly - no NotificationService wrapper
- ✅ Support multiple status changes in consolidated handlers
- ✅ Use match expressions for status-specific logic
- ✅ Include comprehensive context in logs

## 📧 Creating Notifications

### 1. Generate Notification Class
```bash
php artisan make:notification OrderCreatedNotification
```

### 2. Notification Structure
```php
<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Models\Order;
use App\Services\NotificationDecisionEngine;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class OrderCreatedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public readonly Order $order
    ) {}

    public function via(object $notifiable): array
    {
        $engine = app(NotificationDecisionEngine::class);
        return $engine->getChannels('order_created', $notifiable);
    }

    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject("Order Confirmed - #{$this->order->order_reference}")
            ->view('emails.order-confirmation', [
                'order' => $this->order,
                'customer' => $notifiable,
            ]);
    }

    public function toFcm(object $notifiable): FcmMessage
    {
        return FcmMessage::create()
            ->setData([
                'order_id' => $this->order->id,
                'order_reference' => $this->order->order_reference,
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                'route' => "/orders/{$this->order->id}",
            ])
            ->setNotification(
                FcmNotification::create()
                    ->setTitle('Order Confirmed')
                    ->setBody("Your order #{$this->order->order_reference} has been confirmed")
            );
    }

    public function toArray(object $notifiable): array
    {
        return [
            'order_id' => $this->order->id,
            'order_reference' => $this->order->order_reference,
            'created_at' => now()->toISOString(),
        ];
    }
}
```

### 3. Notification Requirements
- ✅ Implement `ShouldQueue` for performance
- ✅ Use `NotificationDecisionEngine` for channel selection
- ✅ Support multiple channels (mail, FCM, database)
- ✅ Include relevant data for mobile apps
- ✅ Use mail templates for consistent branding

## 📄 Creating Mail Templates

### 1. Template Structure
```blade
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title ?? 'DeliveryNexus' }}</title>
    <style>
        /* Responsive email styles */
        body { font-family: 'Segoe UI', sans-serif; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { text-align: center; padding: 20px; }
        .logo { font-size: 24px; font-weight: bold; color: #007bff; }
        .button { background: #28a745; color: white; padding: 12px 30px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🚚 DeliveryNexus</div>
            <h1>{{ $title }}</h1>
        </div>
        
        <div class="content">
            <!-- Template content -->
        </div>
        
        <div class="footer">
            <p>© {{ date('Y') }} DeliveryNexus. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
```

### 2. Template Best Practices
- ✅ Mobile-responsive design
- ✅ Consistent branding and colors
- ✅ Clear call-to-action buttons
- ✅ Accessible HTML structure
- ✅ Dynamic content with Blade syntax

## ⚙️ Event-Listener Registration

### Register Consolidated Events in EventServiceProvider
```php
protected $listen = [
    // Consolidated order events
    OrderStatusUpdated::class => [
        SendOrderStatusNotification::class,
    ],

    // Consolidated payment events
    PaymentStatusChanged::class => [
        SendPaymentNotification::class,
    ],

    // Consolidated subscription events
    UserSubscriptionStatusChanged::class => [
        SendSubscriptionNotification::class . '@handleSubscriptionStatusChanged',
    ],

    // Team member management events
    TeamMemberStatusChanged::class => [
        SendBusinessManagementNotification::class . '@handleTeamMemberStatusChanged',
    ],

    // User creation events
    UserCreatedByAdmin::class => [
        SendUserWelcomeNotification::class . '@handleUserCreatedByAdmin',
    ],
];
```

## 📱 Multi-Channel Strategy

### Channel Selection Logic
```php
// NotificationDecisionEngine determines channels based on:
// 1. Notification type
// 2. User preferences  
// 3. Business rules
// 4. Urgency level

$channels = ['mail', 'fcm']; // Default
if ($urgency === 'high') {
    $channels[] = 'sms'; // Add SMS for critical notifications
}
```

### Supported Channels
- **Email**: All notifications (primary channel)
- **Push (FCM)**: Real-time updates, order status
- **SMS**: OTP verification, order shipped/delivered only
- **Database**: In-app notification history
- **WebSocket**: Real-time broadcasting

## 🧪 Testing

### 1. Event Testing
```php
public function test_order_created_event_is_dispatched(): void
{
    Event::fake();
    
    $order = Order::factory()->create();
    
    // Trigger event
    OrderCreated::dispatch($order, $order->customer);
    
    Event::assertDispatched(OrderCreated::class);
}
```

### 2. Notification Testing
```php
public function test_order_notification_is_sent(): void
{
    Notification::fake();
    
    $user = User::factory()->create();
    $order = Order::factory()->create();
    
    $user->notify(new OrderCreatedNotification($order));
    
    Notification::assertSentTo($user, OrderCreatedNotification::class);
}
```

## ✨ Best Practices

### 1. Consolidated Event Design
- Use consolidated events for status changes (reduces redundancy)
- Include all necessary data in constructor (old/new status, reason, changed by)
- Use readonly properties for immutability
- Implement broadcasting for real-time updates

### 2. Event-Driven Listener Design
- Call `$user->notify()` directly - no NotificationService wrapper
- Handle multiple status changes in consolidated handlers
- Use queues for async processing
- Implement proper error handling with detailed context
- Log notification attempts and failures

### 3. Clean Architecture
- **Controllers**: Only dispatch events, never call NotificationService directly
- **Events**: Carry business context and status change information
- **Listeners**: Handle notifications using Laravel's native `->notify()`
- **NotificationService**: Only for utility functions (OTP, admin features, system notifications)

### 4. Notification Design
- Support multiple channels (email, push, SMS)
- Include mobile-friendly data structures
- Use mail templates for consistent branding
- Handle status-specific content dynamically

### 4. Performance
- Queue all notifications for scalability
- Use database transactions for atomic operations
- Monitor queue health and failures
- Implement retry logic for failed notifications

### 5. Security
- Validate notification recipients
- Sanitize user-generated content
- Use secure channels for sensitive data
- Implement rate limiting for API notifications

---

**Next Steps**: See [notifications.md](./notifications.md) for a complete inventory of all notifications in the system.
