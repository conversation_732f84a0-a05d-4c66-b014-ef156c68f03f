# DeliveryNexus Notifications Inventory

## 📋 Overview
This document provides a comprehensive inventory of all notifications in the DeliveryNexus platform, organized by category and purpose.

**Total Notifications**: 57 (consolidated from 65 - removed 8 redundant notifications)
**Coverage**: Complete business workflows, Real-time operations, Status management, Advanced features, Critical system events
**Architecture**: Event-driven with consolidated status change notifications

---

## 🛒 Order & Delivery Notifications

### OrderStatusUpdated (Consolidated)
- **Purpose**: Handle all order status changes (created, accepted, rejected, shipped, delivered, cancelled)
- **Channels**: Email, Push, SMS (strategic)
- **Recipients**: Customer, Business Owner
- **Template**: `order-status-updated.blade.php` (dynamic based on status)
- **Trigger**: OrderStatusUpdated event
- **Event**: `App\Events\Delivery\OrderStatusUpdated`

### OrderShippedNotification
- **Purpose**: Notify customer when order is shipped
- **Channels**: Email, Push, SMS (strategic)
- **Recipients**: Customer
- **Template**: `order-shipped.blade.php`
- **Trigger**: Order status changes to 'shipped'

### OrderDeliveredNotification
- **Purpose**: Notify customer when order is delivered
- **Channels**: Email, Push, SMS (strategic)
- **Recipients**: Customer
- **Template**: `order-delivered.blade.php`
- **Trigger**: Order status changes to 'delivered'

### OrderCancelledNotification
- **Purpose**: Notify customer and business when order is cancelled
- **Channels**: Email, Push
- **Recipients**: Customer, Business Owner
- **Template**: `order-cancelled.blade.php`
- **Trigger**: Order status changes to 'cancelled'

### DeliveryStatusUpdated
- **Purpose**: Real-time delivery status updates
- **Channels**: Push, WebSocket
- **Recipients**: Customer, Business
- **Template**: None (push only)
- **Trigger**: Delivery status changes

---

## 💳 Payment & Subscription Notifications

### PaymentStatusChangedNotification (Consolidated)
- **Purpose**: Handle all payment status changes (processed, failed, refunded)
- **Channels**: Email, Push, SMS (critical for failures)
- **Recipients**: Customer, Business Owner
- **Template**: `payment-status-changed.blade.php` (dynamic based on status)
- **Trigger**: PaymentStatusChanged event
- **Event**: `App\Events\Financial\PaymentStatusChanged`

### PaymentFailedNotification
- **Purpose**: Alert about failed payment
- **Channels**: Email, Push, SMS (critical)
- **Recipients**: Customer
- **Template**: `payment-failed.blade.php`
- **Trigger**: Payment status changes to 'failed'

### UserSubscriptionStatusChangedNotification (Consolidated)
- **Purpose**: Handle all subscription changes (created, renewed, expired, cancelled, upgraded, downgraded)
- **Channels**: Email, Push, SMS (critical for expiry)
- **Recipients**: Subscriber
- **Template**: `subscription-status-changed.blade.php` (dynamic based on change type)
- **Trigger**: UserSubscriptionStatusChanged event
- **Event**: `App\Events\User\UserSubscriptionStatusChanged`

### SubscriptionCreatedNotification
- **Purpose**: Welcome new subscriber
- **Channels**: Email, Push
- **Recipients**: Subscriber
- **Template**: `subscription-created.blade.php`
- **Trigger**: New subscription created

### SubscriptionRenewedNotification
- **Purpose**: Confirm subscription renewal
- **Channels**: Email, Push
- **Recipients**: Subscriber
- **Template**: `subscription-renewed.blade.php`
- **Trigger**: Subscription renewed successfully

### SubscriptionExpiredNotification
- **Purpose**: Alert about expired subscription
- **Channels**: Email, Push, SMS (critical)
- **Recipients**: Subscriber
- **Template**: `subscription-expired.blade.php`
- **Trigger**: Subscription expires

---

## 👥 User Management Notifications

### WelcomeUser
- **Purpose**: Welcome new users to platform
- **Channels**: Email
- **Recipients**: New User
- **Template**: `welcome-user.blade.php`
- **Trigger**: User registration completed

### EmailVerificationCode
- **Purpose**: Send email verification code
- **Channels**: Email
- **Recipients**: User
- **Template**: Built-in Laravel template
- **Trigger**: Email verification requested

### PasswordResetCode
- **Purpose**: Send password reset code
- **Channels**: Email
- **Recipients**: User
- **Template**: Built-in Laravel template
- **Trigger**: Password reset requested

### DualChannelOtpNotification
- **Purpose**: Send OTP via multiple channels
- **Channels**: Email, SMS
- **Recipients**: User
- **Template**: Built-in template
- **Trigger**: OTP verification required

---

## 🏢 Business Management Notifications

### StaffInvitation
- **Purpose**: Invite new staff members
- **Channels**: Email
- **Recipients**: Invited Staff
- **Template**: `staff-invitation.blade.php`
- **Trigger**: Staff invitation sent

### BusinessVerificationStatusUpdated
- **Purpose**: Update business verification status
- **Channels**: Email, Push
- **Recipients**: Business Owner
- **Template**: `business-verification-completed.blade.php`
- **Trigger**: Verification status changes

### TenantStaffAddedNotification ✅ UNIFIED
- **Purpose**: Welcome new staff member to any tenant (business or provider)
- **Channels**: Email, Database
- **Recipients**: New Staff Member
- **Template**: Dynamic based on tenant type (business/provider portal)
- **Trigger**: TenantStaffAdded event
- **Event**: `App\Events\Tenant\TenantStaffAdded`
- **Features**: Polymorphic tenant support, dynamic login URLs, role-specific content

### TenantTeamMemberStatusChangedNotification ✅ UNIFIED
- **Purpose**: Handle team member status changes for any tenant (activated, deactivated, role_assigned, ownership_transferred, removed)
- **Channels**: Email, Database
- **Recipients**: Team Member
- **Template**: Dynamic based on action and tenant type
- **Trigger**: TenantTeamMemberStatusChanged event
- **Event**: `App\Events\Tenant\TenantTeamMemberStatusChanged`
- **Features**: Action-specific content, tenant-aware URLs, role change details

### TenantStatusChangedNotification ✅ UNIFIED
- **Purpose**: Handle tenant status changes for businesses and providers (active, inactive, suspended, verified, pending, rejected)
- **Channels**: Email, Database
- **Recipients**: Tenant Owner
- **Template**: Dynamic based on status and tenant type
- **Trigger**: TenantStatusChanged event
- **Event**: `App\Events\Tenant\TenantStatusChanged`
- **Features**: Status-specific messaging, tenant-aware portal links, reason inclusion

### UserCredentialsNotification
- **Purpose**: Send login credentials to admin-created users
- **Channels**: Email
- **Recipients**: New User
- **Template**: `user-credentials.blade.php`
- **Trigger**: UserCreatedByAdmin event
- **Event**: `App\Events\User\UserCreatedByAdmin`

### FeedbackSubmittedNotification ⭐ NEW
- **Purpose**: Notify about customer feedback
- **Channels**: Email, Push
- **Recipients**: Business Owner, Delivery Provider
- **Template**: `feedback-submitted.blade.php`
- **Trigger**: Customer submits feedback/rating

---

## 🚚 Delivery Provider Notifications

### ServiceAreaUpdatedNotification ⭐ NEW
- **Purpose**: Notify about service area changes
- **Channels**: Email, Push
- **Recipients**: Provider Owner, Staff
- **Template**: `service-area-updated.blade.php`
- **Trigger**: Service area configuration updated

### VehicleAddedNotification ⭐ NEW
- **Purpose**: Notify about new vehicle addition
- **Channels**: Email, Push
- **Recipients**: Provider Owner, Assigned Driver
- **Template**: `vehicle-added.blade.php`
- **Trigger**: Vehicle added to fleet

### ProviderOnboardingCompletedNotification ⭐ NEW
- **Purpose**: Celebrate completed onboarding
- **Channels**: Email, Push
- **Recipients**: Provider Owner
- **Template**: `provider-onboarding-completed.blade.php`
- **Trigger**: Onboarding process completed

---

## 🔐 Security & API Notifications

### ApiKeyGeneratedNotification ⭐ NEW
- **Purpose**: Notify about new API key generation
- **Channels**: Email, Push
- **Recipients**: API Key Owner
- **Template**: `api-key-generated.blade.php`
- **Trigger**: New API key generated

### KycTierAdvancedNotification ⭐ NEW
- **Purpose**: Notify about KYC tier advancement
- **Channels**: Email, Push
- **Recipients**: User
- **Template**: `kyc-tier-advanced.blade.php`
- **Trigger**: KYC verification tier advanced

### InterstateDeliveryDetectedNotification ⭐ NEW
- **Purpose**: Alert about interstate delivery
- **Channels**: Email, Push
- **Recipients**: Customer, Business, Admin
- **Template**: `interstate-delivery-detected.blade.php`
- **Trigger**: Interstate delivery detected

### WebhookDeliveryFailedNotification ⭐ NEW
- **Purpose**: Alert about webhook failures
- **Channels**: Email, Push
- **Recipients**: API User, Admin
- **Template**: `webhook-delivery-failed.blade.php`
- **Trigger**: Webhook delivery fails after retries

### ApiRateLimitExceededNotification ⭐ NEW
- **Purpose**: Alert about rate limit exceeded
- **Channels**: Email, Push
- **Recipients**: API User, Admin
- **Template**: `api-rate-limit-exceeded.blade.php`
- **Trigger**: API rate limit exceeded

---

## 🔄 Status Change Notifications

### UserStatusChangedNotification ⭐ NEW
- **Purpose**: Notify about user status changes (active, suspended, banned)
- **Channels**: Email, Push
- **Recipients**: User, Admin
- **Template**: `user-status-changed.blade.php`
- **Trigger**: User status changes

### BusinessStatusChangedNotification ⭐ NEW
- **Purpose**: Notify about business status changes
- **Channels**: Email, Push
- **Recipients**: Business Owner, Staff, Admin
- **Template**: `business-status-changed.blade.php`
- **Trigger**: Business status changes

### DeliveryProviderStatusChangedNotification ⭐ NEW
- **Purpose**: Notify about provider status changes
- **Channels**: Email, Push
- **Recipients**: Provider Owner, Drivers, Admin
- **Template**: `provider-status-changed.blade.php`
- **Trigger**: Provider status changes

### TenantStatusChangedNotification ⭐ NEW
- **Purpose**: Notify about tenant status changes
- **Channels**: Email, Push
- **Recipients**: Tenant Users, Admin
- **Template**: `tenant-status-changed.blade.php`
- **Trigger**: Tenant status changes

---

## 🚚 Ad-Hoc Delivery Notifications

### AdHocDeliveryCreatedNotification ⭐ NEW
- **Purpose**: Notify about new ad-hoc delivery request
- **Channels**: Email, Push
- **Recipients**: Customer, Available Providers
- **Template**: `adhoc-delivery-created.blade.php`
- **Trigger**: Ad-hoc delivery created

### AdHocDeliveryProviderAssignedNotification ⭐ NEW
- **Purpose**: Notify about provider assignment
- **Channels**: Email, Push, SMS (urgent)
- **Recipients**: Customer, Driver
- **Template**: `adhoc-delivery-assigned.blade.php`
- **Trigger**: Provider assigned to ad-hoc delivery

### AdHocDeliveryCompletedNotification ⭐ NEW
- **Purpose**: Notify about delivery completion
- **Channels**: Email, Push, SMS
- **Recipients**: Customer, Driver
- **Template**: `adhoc-delivery-completed.blade.php`
- **Trigger**: Ad-hoc delivery completed

---

## 📊 Advanced System Notifications

### ProviderAvailabilityChangedNotification ⭐ NEW
- **Purpose**: Notify about provider availability changes
- **Channels**: Push, WebSocket
- **Recipients**: Dispatch Team, Admin
- **Template**: None (real-time only)
- **Trigger**: Provider availability status changes

### InventoryStockInNotification ⭐ NEW
- **Purpose**: Notify about stock additions
- **Channels**: Email, Push
- **Recipients**: Business Owner, Staff
- **Template**: `inventory-stock-in.blade.php`
- **Trigger**: Stock added to inventory

### InventoryStockOutNotification ⭐ NEW
- **Purpose**: Notify about stock removals
- **Channels**: Email, Push
- **Recipients**: Business Owner, Staff
- **Template**: `inventory-stock-out.blade.php`
- **Trigger**: Stock removed from inventory

### PurchaseOrderCreatedNotification ⭐ NEW
- **Purpose**: Notify about new purchase order
- **Channels**: Email, Push
- **Recipients**: Business Owner, Creator
- **Template**: `purchase-order-created.blade.php`
- **Trigger**: Purchase order created

### VerificationSubmittedNotification ⭐ NEW
- **Purpose**: Notify about verification submission
- **Channels**: Email, Push
- **Recipients**: User, Admin
- **Template**: `verification-submitted.blade.php`
- **Trigger**: Verification documents submitted

### VerificationApprovedNotification ⭐ NEW
- **Purpose**: Notify about verification approval
- **Channels**: Email, Push
- **Recipients**: User
- **Template**: `verification-approved.blade.php`
- **Trigger**: Verification approved

### ReportGenerationCompletedNotification ⭐ NEW
- **Purpose**: Notify about completed report generation
- **Channels**: Email, Push
- **Recipients**: Report Requester
- **Template**: `report-completed.blade.php`
- **Trigger**: Report generation completed

### AiPredictionGeneratedNotification ⭐ NEW
- **Purpose**: Notify about AI prediction results
- **Channels**: Email, Push
- **Recipients**: Business Owner
- **Template**: `ai-prediction-generated.blade.php`
- **Trigger**: AI prediction generated

### DisputeCreatedNotification ⭐ NEW
- **Purpose**: Notify about new dispute
- **Channels**: Email, Push
- **Recipients**: Customer, Business, Admin
- **Template**: `dispute-created.blade.php`
- **Trigger**: Dispute created

### ReferralSuccessfulNotification ⭐ NEW
- **Purpose**: Notify about successful referral
- **Channels**: Email, Push
- **Recipients**: Referrer, Referee
- **Template**: `referral-successful.blade.php`
- **Trigger**: Referral completed successfully

### FraudDetectedNotification ⭐ NEW
- **Purpose**: Alert about fraud detection (ADMIN ONLY)
- **Channels**: Email, Push
- **Recipients**: Admin, Security Team
- **Template**: `fraud-detected.blade.php`
- **Trigger**: Fraudulent activity detected

### CommissionCalculatedNotification ⭐ NEW
- **Purpose**: Notify about commission calculation
- **Channels**: Email, Push
- **Recipients**: Commission Recipient
- **Template**: `commission-calculated.blade.php`
- **Trigger**: Commission calculated

### PromotionAppliedNotification ⭐ NEW
- **Purpose**: Notify about promotion/discount applied
- **Channels**: Email, Push
- **Recipients**: Customer
- **Template**: `promotion-applied.blade.php`
- **Trigger**: Promotion applied to order

### BusinessOnboardingCompletedNotification ⭐ NEW
- **Purpose**: Celebrate business onboarding completion
- **Channels**: Email, Push
- **Recipients**: Business Owner, Staff
- **Template**: `business-onboarding-completed.blade.php`
- **Trigger**: Business onboarding completed

### BusinessSetupStepCompletedNotification ⭐ NEW
- **Purpose**: Notify about setup step completion
- **Channels**: Email, Push
- **Recipients**: Business Owner
- **Template**: `business-setup-step-completed.blade.php`
- **Trigger**: Business setup step completed

---

## 📊 Notification Statistics

### By Category
- **Order & Delivery**: 4 notifications
- **Payment & Subscription**: 6 notifications
- **User Management**: 4 notifications
- **Business Management**: 6 notifications ⬆️ (+2)
- **Delivery Provider**: 3 notifications
- **Security & API**: 5 notifications
- **Status Changes**: 4 notifications ⭐ NEW
- **Ad-Hoc Deliveries**: 3 notifications ⭐ NEW
- **Advanced Systems**: 15 notifications ⭐ NEW

### By Channel Support
- **Email**: 50/50 (100%)
- **Push Notifications**: 48/50 (96%)
- **SMS**: 8/50 (16% - strategic only)
- **WebSocket**: 50/50 (100% via broadcasting)

### By Priority Level
- **Critical**: 15 notifications (fraud, status changes, security alerts)
- **High**: 20 notifications (order updates, payments, subscriptions)
- **Medium**: 15 notifications (business operations, reports, AI predictions)

---

## 🔄 Notification Flow Examples

### Order Lifecycle
1. `OrderCreated` → Customer & Business notified
2. `OrderAccepted` → Customer notified
3. `OrderShipped` → Customer notified (Email + SMS)
4. `OrderDelivered` → Customer notified (Email + SMS)
5. `FeedbackSubmitted` → Business notified

### Subscription Lifecycle
1. `SubscriptionCreated` → Welcome email
2. `SubscriptionRenewed` → Renewal confirmation
3. `SubscriptionExpired` → Expiry alert (Email + SMS)
4. `PaymentFailed` → Payment failure alert

### Business Onboarding
1. `UserRegistered` → Welcome email
2. `BusinessVerificationCompleted` → Verification success
3. `StaffAdded` → Staff welcome
4. `ApiKeyGenerated` → API access granted

---

## 🎯 Implementation Status

### ✅ Fully Implemented (15)
All existing notifications are fully functional with proper templates and multi-channel support.

### ⭐ Newly Implemented (35)
All missing notifications have been created with:
- Complete notification classes
- Mail templates (where applicable)
- Event-listener integration
- Multi-channel support
- Real-time broadcasting
- Queue-based processing

### 🔄 Future Enhancements
- Advanced personalization
- A/B testing for templates
- Analytics and tracking
- Webhook notification system
- Custom notification preferences

---

**Next Steps**: See [mails.md](./mails.md) for detailed mail template inventory and [events.md](./events.md) for complete event system documentation.