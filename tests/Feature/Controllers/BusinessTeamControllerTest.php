<?php

declare(strict_types=1);

namespace Tests\Feature\Controllers;

use App\Enums\Business\TeamMemberRelationshipType;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessTeamController;
use App\Models\Business\Business;
use App\Models\Business\BusinessBranch;
use App\Models\Business\BusinessTeamMember;
use App\Models\Core\Country;
use App\Models\Core\State;
use App\Models\System\Tenant;
use App\Models\User\User;
use App\Services\Business\BusinessService;
use App\Services\Communication\NotificationService;
use App\Services\System\LoggingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Laravel\Sanctum\Sanctum;
use PHPUnit\Framework\Attributes\Test;
use Stancl\Tenancy\Facades\Tenancy;

class BusinessTeamControllerTest extends \Tests\TestCase
{
    use RefreshDatabase;

    private BusinessTeamController $controller;

    private Tenant $tenant;

    private User $businessUser;

    private Business $business;

    private BusinessBranch $branch;

    private Country $country;

    private State $state;

    private User $teamMember1;

    private User $teamMember2;

    private BusinessTeamMember $businessTeamMember1;

    private BusinessTeamMember $businessTeamMember2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test tenant and initialize tenancy
        $this->tenant = Tenant::create([
            'id' => 'test-business-tenant',
            'name' => 'Test Business Tenant',
            'tenant_type' => 'business',
        ]);

        // Create tenant domain
        $this->tenant->domains()->create([
            'domain' => 'test-business-tenant.localhost',
            'is_primary' => true,
            'verification_status' => \App\Enums\System\DomainVerificationStatus::VERIFIED,
        ]);

        Tenancy::initialize($this->tenant);

        // Create test user (business owner)
        $this->businessUser = User::factory()->create([
            'tenant_id' => $this->tenant->id,
            'first_name' => 'Business',
            'last_name' => 'Owner',
            'email' => '<EMAIL>',
        ]);

        // Create test country and state (or use existing ones)
        $this->country = Country::firstOrCreate(
            ['code' => 'NG'],
            [
                'name' => 'Nigeria',
                'currency_code' => 'NGN',
                'phone_code' => '234',
                'timezone' => 'Africa/Lagos',
                'is_active' => true,
            ]
        );
        $this->state = State::firstOrCreate(
            ['country_id' => $this->country->id, 'code' => 'LA'],
            ['name' => 'Lagos', 'is_active' => true]
        );

        // Create test business
        $this->business = Business::factory()->create([
            'tenant_id' => $this->tenant->id,
            'user_id' => $this->businessUser->id,
            'country_id' => $this->country->id,
            'state_id' => $this->state->id,
            'business_name' => 'Test Business',
            'business_type' => \App\Enums\Business\BusinessType::FOOD,
            'status' => \App\Enums\Business\BusinessStatus::ACTIVE,
        ]);

        // Create test business branch
        $this->branch = BusinessBranch::factory()->create([
            'business_id' => $this->business->id,
            'tenant_id' => $this->tenant->id,
        ]);

        // Create additional team members for testing
        $this->teamMember1 = User::factory()->create([
            'tenant_id' => $this->tenant->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        $this->teamMember2 = User::factory()->create([
            'tenant_id' => $this->tenant->id,
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'is_active' => false,
        ]);

        // Create BusinessTeamMember records
        $this->businessTeamMember1 = BusinessTeamMember::create([
            'tenant_id' => $this->tenant->id,
            'business_id' => $this->business->id,
            'user_id' => $this->teamMember1->id,
            'role' => TeamMemberRelationshipType::STAFF,
        ]);

        $this->businessTeamMember2 = BusinessTeamMember::create([
            'tenant_id' => $this->tenant->id,
            'business_id' => $this->business->id,
            'user_id' => $this->teamMember2->id,
            'role' => TeamMemberRelationshipType::STAFF,
        ]);

        // Create BusinessTeamMember record for business owner
        BusinessTeamMember::create([
            'tenant_id' => $this->tenant->id,
            'business_id' => $this->business->id,
            'user_id' => $this->businessUser->id,
            'role' => TeamMemberRelationshipType::ADMIN,
        ]);

        // Authenticate user
        Sanctum::actingAs($this->businessUser);

        // Create controller instance
        $businessService = app(BusinessService::class);
        $loggingService = app(LoggingService::class);
        $notificationService = app(NotificationService::class);
        $teamInvitationService = app(\App\Services\User\TeamInvitationService::class);
        $this->controller = new BusinessTeamController($businessService, $loggingService, $notificationService, $teamInvitationService);
    }

    protected function tearDown(): void
    {
        Tenancy::end();
        parent::tearDown();
    }

    #[Test]
    public function it_can_retrieve_team_members_list(): void
    {
        $request = new Request;

        $response = $this->controller->index($request);

        expect($response->getStatusCode())->toBe(200);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeTrue();
        expect($responseData['message'])->toBe('Team members retrieved successfully');
        expect($responseData['data'])->toHaveKeys([
            'data',
            'current_page',
            'per_page',
            'total',
            'last_page',
        ]);
        expect($responseData['data']['total'])->toBe(3); // Owner + 2 team members
    }

    #[Test]
    public function it_can_search_team_members_by_name(): void
    {
        $request = new Request(['search' => 'John']);

        $response = $this->controller->index($request);

        expect($response->getStatusCode())->toBe(200);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeTrue();
        expect($responseData['data']['total'])->toBe(1);
    }

    #[Test]
    public function it_can_filter_team_members_by_status(): void
    {
        $request = new Request(['status' => 'active']);

        $response = $this->controller->index($request);

        expect($response->getStatusCode())->toBe(200);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeTrue();
        expect($responseData['data']['total'])->toBe(2); // Owner + John (active members)
    }

    #[Test]
    public function it_validates_pagination_parameters(): void
    {
        $request = new Request(['per_page' => 150]); // Exceeds max

        try {
            $response = $this->controller->index($request);
            expect(false)->toBeTrue('Expected validation exception');
        } catch (\Illuminate\Validation\ValidationException $e) {
            expect($e->errors())->toHaveKey('per_page');
        }
    }

    #[Test]
    public function it_validates_status_filter(): void
    {
        $request = new Request(['status' => 'invalid']);

        try {
            $response = $this->controller->index($request);
            expect(false)->toBeTrue('Expected validation exception');
        } catch (\Illuminate\Validation\ValidationException $e) {
            expect($e->errors())->toHaveKey('status');
        }
    }

    #[Test]
    public function it_returns_error_when_no_business_found_for_index(): void
    {
        // Delete the business
        $this->business->delete();

        $request = new Request;
        $response = $this->controller->index($request);

        expect($response->getStatusCode())->toBe(422);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeFalse();
        expect($responseData['message'])->toBe('No business found for current tenant');
    }

    #[Test]
    public function it_can_retrieve_specific_team_member(): void
    {
        $response = $this->controller->show($this->businessTeamMember1->id);

        expect($response->getStatusCode())->toBe(200);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeTrue();
        expect($responseData['message'])->toBe('Team member retrieved successfully');
        expect($responseData['data']['id'])->toBe($this->teamMember1->id);
        expect($responseData['data']['first_name'])->toBe('John');
        expect($responseData['data']['last_name'])->toBe('Doe');
        expect($responseData['data']['email'])->toBe('<EMAIL>');
    }

    #[Test]
    public function it_returns_404_for_non_existent_team_member(): void
    {
        $nonExistentId = '01234567-89ab-cdef-0123-456789abcdef'; // Valid UUID format but doesn't exist
        $response = $this->controller->show($nonExistentId);

        expect($response->getStatusCode())->toBe(404);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeFalse();
        expect($responseData['message'])->toBe('Team member not found');
    }

    #[Test]
    public function it_returns_error_when_no_business_found_for_show(): void
    {
        // Delete the business
        $this->business->delete();

        $response = $this->controller->show($this->businessTeamMember1->id);

        expect($response->getStatusCode())->toBe(422);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeFalse();
        expect($responseData['message'])->toBe('No business found for current tenant');
    }

    #[Test]
    public function it_validates_required_fields_for_store_method(): void
    {
        $request = new Request([]);

        try {
            $response = $this->controller->store($request);
            expect(false)->toBeTrue('Expected validation exception');
        } catch (\Illuminate\Validation\ValidationException $e) {
            expect($e->errors())->not->toBeEmpty();
        }
    }

    #[Test]
    public function it_returns_404_for_update_method_with_invalid_team_member_id(): void
    {
        $nonExistentId = '01234567-89ab-cdef-0123-456789abcdef'; // Valid UUID format but doesn't exist
        $request = new Request;
        $response = $this->controller->update($request, $nonExistentId);

        expect($response->getStatusCode())->toBe(404);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeFalse();
        expect($responseData['message'])->toBe('Team member not found');
    }

    #[Test]
    public function it_returns_404_for_destroy_method_with_invalid_team_member_id(): void
    {
        $nonExistentId = '01234567-89ab-cdef-0123-456789abcdef'; // Valid UUID format but doesn't exist
        $response = $this->controller->destroy($nonExistentId);

        expect($response->getStatusCode())->toBe(404);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeFalse();
        expect($responseData['message'])->toBe('Team member not found');
    }

    #[Test]
    public function it_returns_501_for_assign_role_method(): void
    {
        $request = new Request;
        $response = $this->controller->assignRole($request, $this->businessTeamMember1->id);

        expect($response->getStatusCode())->toBe(501);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeFalse();
        expect($responseData['message'])->toBe('Role assignment not implemented yet');
    }

    #[Test]
    public function it_returns_501_for_remove_role_method(): void
    {
        $request = new Request;
        $response = $this->controller->removeRole($request, $this->businessTeamMember1->id);

        expect($response->getStatusCode())->toBe(501);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeFalse();
        expect($responseData['message'])->toBe('Role removal not implemented yet');
    }

    #[Test]
    public function it_can_activate_inactive_team_member(): void
    {
        // Make team member inactive first
        $this->teamMember1->update(['is_active' => false]);

        $response = $this->controller->activate($this->businessTeamMember1->id);

        expect($response->getStatusCode())->toBe(200);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeTrue();
        expect($responseData['message'])->toBe('Team member activated successfully');
        expect($responseData['data']['is_active'])->toBeTrue();
    }

    #[Test]
    public function it_returns_error_when_activating_already_active_member(): void
    {
        // Ensure team member is active
        $this->teamMember1->update(['is_active' => true]);

        $response = $this->controller->activate($this->businessTeamMember1->id);

        expect($response->getStatusCode())->toBe(422);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeFalse();
        expect($responseData['message'])->toBe('Team member is already active');
    }

    #[Test]
    public function it_can_deactivate_active_team_member(): void
    {
        // Ensure team member is active and not business owner
        $this->teamMember1->update(['is_active' => true]);

        $response = $this->controller->deactivate($this->businessTeamMember1->id);

        expect($response->getStatusCode())->toBe(200);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeTrue();
        expect($responseData['message'])->toBe('Team member deactivated successfully');
        expect($responseData['data']['is_active'])->toBeFalse();
    }

    #[Test]
    public function it_returns_error_when_deactivating_already_inactive_member(): void
    {
        // Make team member inactive first
        $this->teamMember1->update(['is_active' => false]);

        $response = $this->controller->deactivate($this->businessTeamMember1->id);

        expect($response->getStatusCode())->toBe(422);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeFalse();
        expect($responseData['message'])->toBe('Team member is already inactive');
    }

    #[Test]
    public function it_can_get_business_admins(): void
    {
        // Assign admin role to business user
        $this->businessUser->assign('business-owner');

        $response = $this->controller->getAdmins();

        expect($response->getStatusCode())->toBe(200);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeTrue();
        expect($responseData['message'])->toBe('Business admins retrieved successfully');
        expect($responseData['data'])->toBeArray();
    }

    #[Test]
    public function it_validates_required_fields_for_assign_admin(): void
    {
        $request = new Request;

        try {
            $response = $this->controller->assignAdmin($request);
            expect(false)->toBeTrue('Expected validation exception');
        } catch (\Illuminate\Validation\ValidationException $e) {
            expect($e->errors())->toHaveKey('user_id');
            expect($e->errors())->toHaveKey('role');
        }
    }

    #[Test]
    public function it_validates_required_fields_for_transfer_ownership(): void
    {
        $request = new Request;

        try {
            $response = $this->controller->transferOwnership($request);
            expect(false)->toBeTrue('Expected validation exception');
        } catch (\Illuminate\Validation\ValidationException $e) {
            expect($e->errors())->toHaveKey('new_owner_id');
            expect($e->errors())->toHaveKey('confirmation_password');
        }
    }

    #[Test]
    public function it_returns_501_for_update_admin_role_method(): void
    {
        $request = new Request;
        $response = $this->controller->updateAdminRole($request, $this->businessUser->id);

        expect($response->getStatusCode())->toBe(501);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeFalse();
        expect($responseData['message'])->toBe('Admin role update not implemented yet');
    }

    #[Test]
    public function it_returns_501_for_remove_admin_method(): void
    {
        $response = $this->controller->removeAdmin($this->businessUser->id);

        expect($response->getStatusCode())->toBe(501);

        $responseData = json_decode($response->getContent(), true);
        expect($responseData['success'])->toBeFalse();
        expect($responseData['message'])->toBe('Admin removal not implemented yet');
    }
}
