<?php

declare(strict_types=1);

use App\Models\User\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Lara<PERSON>\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Disable external services for testing
    Config::set('services.twilio.enabled', false);
    Config::set('services.zeptomail.enabled', false);

    // Create a test user
    $this->user = User::factory()->create([
        'email_verified_at' => null,
        'phone_verified_at' => null,
        'phone_number' => '+2348123456789',
    ]);
});

test('user can request email verification', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/v1/auth/email/send-verification');

    $response->assertOk()
        ->assertJsonStructure([
            'success',
            'message',
        ])
        ->assertJsonPath('message', 'Email verification code sent successfully');

    // Verify OTP was cached
    $cacheKey = "email_verification:{$this->user->id}";
    expect(Cache::has($cacheKey))->toBeTrue();
});

test('user can verify email with correct OTP', function () {
    Sanctum::actingAs($this->user);

    // Manually set OTP in cache
    $otp = '123456';
    $cacheKey = "email_verification:{$this->user->id}";
    Cache::put($cacheKey, $otp, 900);

    $response = $this->postJson('/api/v1/auth/email/verify', [
        'code' => $otp,
    ]);

    $response->assertOk()
        ->assertJsonPath('message', 'Email verified successfully');

    // Verify user is now email verified
    expect($this->user->fresh()->hasVerifiedEmail())->toBeTrue();

    // Verify OTP was cleared from cache
    expect(Cache::has($cacheKey))->toBeFalse();
});

test('email verification fails with incorrect OTP', function () {
    Sanctum::actingAs($this->user);

    // Set correct OTP in cache
    $correctOtp = '123456';
    $cacheKey = "email_verification:{$this->user->id}";
    Cache::put($cacheKey, $correctOtp, 900);

    $response = $this->postJson('/api/v1/auth/email/verify', [
        'code' => '654321', // Wrong OTP
    ]);

    $response->assertUnprocessable()
        ->assertJsonPath('message', 'Invalid or expired verification code');

    // Verify user is still not verified
    expect($this->user->fresh()->hasVerifiedEmail())->toBeFalse();
});

test('user can request phone verification', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/v1/auth/phone/send-verification');

    $response->assertOk()
        ->assertJsonStructure([
            'success',
            'message',
        ])
        ->assertJsonPath('message', 'Phone verification code sent successfully');

    // Verify OTP was cached (VerificationService uses phone_otp:{phone_number})
    $cacheKey = "phone_otp:{$this->user->phone_number}";
    expect(Cache::has($cacheKey))->toBeTrue();
});

test('user can verify phone with correct OTP', function () {
    Sanctum::actingAs($this->user);

    // Manually set OTP in cache
    $otp = '654321';
    $cacheKey = "phone_otp:{$this->user->phone_number}";
    Cache::put($cacheKey, $otp, 900);

    $response = $this->postJson('/api/v1/auth/phone/verify', [
        'code' => $otp,
    ]);

    $response->assertOk()
        ->assertJsonPath('message', 'Phone verified successfully');

    // Verify user is now phone verified
    expect($this->user->fresh()->hasVerifiedPhone())->toBeTrue();

    // Verify OTP was cleared from cache
    expect(Cache::has($cacheKey))->toBeFalse();
});

test('phone verification fails with incorrect OTP', function () {
    Sanctum::actingAs($this->user);

    // Set correct OTP in cache
    $correctOtp = '654321';
    $cacheKey = "phone_otp:{$this->user->phone_number}";
    Cache::put($cacheKey, $correctOtp, 900);

    $response = $this->postJson('/api/v1/auth/phone/verify', [
        'code' => '123456', // Wrong OTP
    ]);

    $response->assertUnprocessable()
        ->assertJsonPath('message', 'Invalid or expired verification code');

    // Verify user is still not verified
    expect($this->user->fresh()->hasVerifiedPhone())->toBeFalse();
});

test('verification requests are rate limited', function () {
    // Temporarily change environment to enable rate limiting
    app()->detectEnvironment(function () {
        return 'production';
    });

    Sanctum::actingAs($this->user);

    // Make 3 requests (should be allowed)
    for ($i = 0; $i < 3; $i++) {
        $response = $this->postJson('/api/v1/auth/email/send-verification');
        $response->assertOk();
    }

    // 4th request should be rate limited
    $response = $this->postJson('/api/v1/auth/email/send-verification');
    $response->assertStatus(429);
})->skip('Rate limiting is disabled in testing environment');

test('verification attempts are rate limited', function () {
    Sanctum::actingAs($this->user);

    // Set OTP in cache
    $cacheKey = "email_verification:{$this->user->id}";
    Cache::put($cacheKey, '123456', 900);

    // Make 5 failed attempts (should be allowed)
    for ($i = 0; $i < 5; $i++) {
        $response = $this->postJson('/api/v1/auth/email/verify', [
            'code' => '000000', // Wrong OTP
        ]);
        $response->assertUnprocessable();
    }

    // 6th attempt should be rate limited
    $response = $this->postJson('/api/v1/auth/email/verify', [
        'code' => '000000',
    ]);
    $response->assertStatus(429);
})->skip('Rate limiting is disabled in testing environment');

test('OTP validation requires correct format', function () {
    Sanctum::actingAs($this->user);

    // Test invalid OTP formats
    $invalidOtps = [
        '12345',      // Too short
        '1234567',    // Too long
        'abcdef',     // Non-numeric
        '12345a',     // Mixed
        '',           // Empty
    ];

    foreach ($invalidOtps as $invalidOtp) {
        $response = $this->postJson('/api/v1/auth/email/verify', [
            'code' => $invalidOtp,
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['code']);
    }
});

test('verification service generates valid OTP', function () {
    Sanctum::actingAs($this->user);

    // Send verification to trigger OTP generation
    $response = $this->postJson('/api/v1/auth/email/send-verification');
    $response->assertOk();

    // Check that OTP was stored in cache
    $cacheKey = "email_verification:{$this->user->id}";
    $otp = Cache::get($cacheKey);

    // Verify OTP is 6 digits
    expect($otp)->toMatch('/^\d{6}$/');
    expect(strlen($otp))->toBe(6);
});

test('expired OTP is rejected', function () {
    Sanctum::actingAs($this->user);

    // Set OTP in cache with very short expiration
    $otp = '123456';
    $cacheKey = "email_verification:{$this->user->id}";
    Cache::put($cacheKey, $otp, 1); // 1 second

    // Wait for expiration
    sleep(2);

    $response = $this->postJson('/api/v1/auth/email/verify', [
        'code' => $otp,
    ]);

    $response->assertUnprocessable()
        ->assertJsonPath('message', 'Invalid or expired verification code');
});

test('already verified email cannot be verified again', function () {
    $user = User::factory()->create([
        'email_verified_at' => now(),
    ]);
    Sanctum::actingAs($user);

    $response = $this->postJson('/api/v1/auth/email/send-verification');

    $response->assertUnprocessable()
        ->assertJsonPath('message', 'Email is already verified');
});

test('already verified phone cannot be verified again', function () {
    $user = User::factory()->create([
        'phone_number' => '+2348012345678',
        'phone_verified_at' => now(),
    ]);
    Sanctum::actingAs($user);

    $response = $this->postJson('/api/v1/auth/phone/send-verification');

    $response->assertUnprocessable()
        ->assertJsonPath('message', 'Phone number is already verified');
});

test('user without phone number cannot request phone verification', function () {
    $user = User::factory()->withoutPhone()->create();
    Sanctum::actingAs($user);

    $response = $this->postJson('/api/v1/auth/phone/send-verification');

    $response->assertUnprocessable()
        ->assertJsonPath('message', 'User does not have a phone number');
});
